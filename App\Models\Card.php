<?php

namespace App\Models;

class Card
{
    public string $name;
    public int $id;
    public int $elixirCost;
    public array $Attack;
    public int $units;
    public bool|int $duration;
    public bool|int $DeploymentTime;
    public bool $evolution;
    public bool $slotEvolved;
    public int|bool $cycles;
    public string $TypeAttack;
    public bool $proyectil;
    public bool $suicide;
    public int $FatalDamage;
    public int $TowerDamage;
    public int $damage;
    public int $hitpoints;
    public int $dps;
    public object $special;
    public object $specialEvo;
    public int|float $hitspeed;
    public int|string $radius;
    public int|bool $GenerationSpeed;
    public int|bool $GenerationUnits;
    public string $speed;
    public string|int $range;
    public string $territory;
    public string $rarity;
    public string $type;
    public string $urlIcon;
    public string $description;

    public function __construct(string|int $nameId, int $level = 11, bool $evolved = false)
    {
        $jsonStats = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
        $jsonMedia = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/mediaCards.json'));

        $allCards = array_merge($jsonStats->towerCards, $jsonStats->cards);
        foreach ($allCards as $card) {
            if ($card->name == $nameId || $card->id == $nameId) {
                if ($evolved && !$card->evolution)
                    throw new \Exception("Card " . $nameId . " can't be evolved");

                foreach ($card as $key => $value) {
                    if (!in_array($key, ['hitpoints', 'dps', 'damage', 'FatalDamage', 'TowerDamage', 'statsEvo'])) {
                        $this->{$key} = $value;
                    }
                }
                $this->slotEvolved = $evolved;
                $this->hitpoints = $evolved ? $card->statsEvo->hitpoints->{"level$level"} : $card->hitpoints->{"level$level"};
                $this->damage = $evolved ? $card->statsEvo->damage->{"level$level"} : $card->damage->{"level$level"};
                $this->dps = $evolved ? $card->statsEvo->dps->{"level$level"} : $card->dps->{"level$level"};
                $this->FatalDamage = $evolved ? $card->FatalDamage->{"level$level"} : $card->FatalDamage->{"level$level"};
                $this->TowerDamage = $evolved ? $card->TowerDamage->{"level$level"} : $card->TowerDamage->{"level$level"};
                $this->cycles = $evolved ? $card->statsEvo->cycles : false;
                $this->specialEvo = $evolved ? $card->statsEvo->specialEvo : (object) [];
                break;
            }
        }

        is_string($nameId) && $this->description = $jsonMedia->{$nameId}->description;
        is_string($nameId) && $this->urlIcon = $evolved ? $jsonMedia->{$nameId}->iconUrls->evolutionMedium : $jsonMedia->{$nameId}->iconUrls->medium;
    }
}
