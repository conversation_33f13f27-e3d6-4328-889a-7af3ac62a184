# Instala la librería (si no la tienes):
# pip install mysql-connector-python python-dotenv # dotenv es para leer .envimport os
# Crea un archivo .env en la raíz de tu proyecto para las credenciales (¡añádelo a tu .gitignore para no subirlo a Git!):
""" DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=clash_strategic
DB_USER=tu_usuario_mysql
DB_PASSWORD=tu_contraseña_mysql """

# Ejecuta: python run_migrations.py
#El script se conectará, comprobará qué falta y aplicará las migraciones pendientes en orden, registrándolas en schema_migrations.

import os
import sys
import mysql.connector
from mysql.connector import errorcode
from dotenv import load_dotenv
import logging

# --- Configuración ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
load_dotenv() # Carga variables desde el archivo .env

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'database': os.getenv('DB_DATABASE'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD')
}
MIGRATIONS_DIR = "database/migrations"
SCHEMA_TABLE = "schema_migrations"

# --- Funciones Auxiliares ---

def get_db_connection():
    """Establece la conexión con la base de datos."""
    if not DB_CONFIG['database'] or not DB_CONFIG['user']:
        logging.error("Error: Faltan variables de entorno DB_DATABASE o DB_USER.")
        sys.exit(1)
    try:
        cnx = mysql.connector.connect(**DB_CONFIG)
        logging.info(f"Conectado a la base de datos '{DB_CONFIG['database']}' en {DB_CONFIG['host']}:{DB_CONFIG['port']}")
        return cnx
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            logging.error("Error de acceso: Usuario o contraseña incorrectos.")
        elif err.errno == errorcode.ER_BAD_DB_ERROR:
            logging.error(f"Error: La base de datos '{DB_CONFIG['database']}' no existe.")
        else:
            logging.error(f"Error de conexión a la base de datos: {err}")
        sys.exit(1)

def ensure_schema_table(cursor):
    """Asegura que la tabla de seguimiento de migraciones exista."""
    try:
        cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS {SCHEMA_TABLE} (
                version VARCHAR(255) PRIMARY KEY NOT NULL,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """)
        logging.info(f"Tabla '{SCHEMA_TABLE}' verificada/creada.")
    except mysql.connector.Error as err:
        logging.error(f"Error al crear/verificar la tabla {SCHEMA_TABLE}: {err}")
        sys.exit(1)

def get_applied_migrations(cursor):
    """Obtiene la lista de versiones de migración ya aplicadas."""
    try:
        cursor.execute(f"SELECT version FROM {SCHEMA_TABLE} ORDER BY version")
        # Usamos un set para búsqueda rápida O(1)
        applied = {row[0] for row in cursor.fetchall()}
        logging.info(f"Migraciones aplicadas encontradas: {len(applied)}")
        return applied
    except mysql.connector.Error as err:
        # Si la tabla no existe aún (primera ejecución), puede fallar.
        # ensure_schema_table debería prevenir esto, pero es bueno ser robusto.
        if err.errno == errorcode.ER_NO_SUCH_TABLE:
             logging.warning(f"La tabla {SCHEMA_TABLE} no existía al buscar aplicadas (se creará).")
             return set() # Devolver conjunto vacío
        logging.error(f"Error al obtener migraciones aplicadas: {err}")
        sys.exit(1)


def get_available_migrations(migrations_dir):
    """Obtiene y ordena los archivos de migración disponibles en el directorio."""
    try:
        files = [f for f in os.listdir(migrations_dir) if f.endswith('.sql')]
        files.sort() # Ordena alfabéticamente (gracias al timestamp YYYYMMDDHHMMSS)
        logging.info(f"Archivos de migración encontrados en '{migrations_dir}': {len(files)}")
        return files
    except FileNotFoundError:
        logging.error(f"Error: El directorio de migraciones '{migrations_dir}' no fue encontrado.")
        sys.exit(1)
    except Exception as e:
         logging.error(f"Error al leer el directorio de migraciones: {e}")
         sys.exit(1)

# --- Lógica Principal ---

def run():
    """Ejecuta el proceso de migración."""
    cnx = None
    cursor = None
    try:
        cnx = get_db_connection()
        cursor = cnx.cursor()

        ensure_schema_table(cursor)
        applied_versions = get_applied_migrations(cursor)
        available_migrations = get_available_migrations(MIGRATIONS_DIR)

        pending_migrations = [m for m in available_migrations if m not in applied_versions]

        if not pending_migrations:
            logging.info("La base de datos ya está actualizada. No hay migraciones pendientes.")
            return

        logging.info(f"Migraciones pendientes encontradas: {len(pending_migrations)}")

        for migration_file in pending_migrations:
            filepath = os.path.join(MIGRATIONS_DIR, migration_file)
            logging.info(f"--- Aplicando migración: {migration_file} ---")

            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    sql_content = f.read()

                if not sql_content.strip():
                     logging.warning(f"Archivo de migración vacío: {migration_file}. Saltando.")
                     # Opcionalmente, registrarlo como aplicado si se desea
                     # cursor.execute(f"INSERT INTO {SCHEMA_TABLE} (version) VALUES (%s)", (migration_file,))
                     # cnx.commit()
                     continue

                # Usar transacciones si es posible (InnoDB soporta DDL transaccional limitado en MySQL 8+)
                # Para mayor seguridad, aunque DDL no siempre es transaccional en MySQL < 8
                cnx.start_transaction()

                # Ejecutar el contenido SQL. Usar multi=True para manejar múltiples sentencias en un archivo.
                # El conector devuelve un iterador de resultados por cada sentencia.
                results = cursor.execute(sql_content, multi=True)
                for i, res in enumerate(results):
                    logging.debug(f"Resultado sentencia {i+1} en {migration_file}: rows={res.rowcount}, id={res.lastrowid}")
                    # Necesitas consumir el iterador incluso si no te importan los resultados
                    pass

                # Si el SQL se ejecutó sin errores, registrar la migración
                cursor.execute(f"INSERT INTO {SCHEMA_TABLE} (version) VALUES (%s)", (migration_file,))

                # Confirmar la transacción (SQL + registro)
                cnx.commit()
                logging.info(f"OK: {migration_file} aplicada y registrada.")

            except mysql.connector.Error as err:
                logging.error(f"!!!! ERROR al aplicar {migration_file}: {err} !!!!")
                logging.error("!!!! REVERTIENDO TRANSACCIÓN (si es posible) !!!!")
                if cnx:
                    cnx.rollback() # Intentar revertir
                # ¡La base de datos podría estar en estado inconsistente si falló DDL!
                logging.error("!!!! Proceso de migración detenido. Revisa el error y el estado de la BD manualmente. !!!!")
                sys.exit(1) # Detener en el primer error
            except Exception as e:
                 logging.error(f"!!!! ERROR inesperado leyendo o procesando {migration_file}: {e} !!!!")
                 if cnx:
                    cnx.rollback()
                 sys.exit(1)


        logging.info("--- Proceso de migración completado exitosamente. ---")

    except Exception as e:
         logging.error(f"Error general en el proceso de migración: {e}")
         sys.exit(1)
    finally:
        # Asegurar que la conexión se cierre siempre
        if cursor:
            cursor.close()
        if cnx and cnx.is_connected():
            cnx.close()
            logging.info("Conexión a la base de datos cerrada.")


if __name__ == "__main__":
    run()