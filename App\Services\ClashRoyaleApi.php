<?php

namespace App\Services;

class ClashRoyaleApi
{
    private $apiKey;
    private $baseUrl = 'https://api.clashroyale.com/v1/';

    public function __construct(string $apiKey)
    {
        $this->apiKey = $apiKey;
    }

    public function getPlayerData(string $playerTag, array $endpoints = ['profile'])
    {
        $playerTag = urlencode(ltrim($playerTag, '#'));
        $results = [];

        foreach ($endpoints as $endpoint) {
            $url = $this->buildEndpointUrl($playerTag, $endpoint);
            $response = $this->makeApiCall($url);

            $results[$endpoint] = $response ?? ['error' => 'Failed to retrieve data'];
        }

        return $results;
    }

    private function buildEndpointUrl(string $playerTag, string $endpoint): string
    {
        $endpoints = [
            'profile' => "players/{$playerTag}",
            'chests' => "players/{$playerTag}/upcomingchests",
            'battles' => "players/{$playerTag}/battlelog",
            // Añadir más endpoints aquí
        ];

        return $this->baseUrl . ($endpoints[$endpoint] ?? $endpoint);
    }

    private function makeApiCall(string $url)
    {
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Accept: application/json'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_FAILONERROR => true,
            CURLOPT_TIMEOUT => 10
        ]);

        $response = curl_exec($ch);

        if ($response === false) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception("cURL error: $error");
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 400) {
            $decodedResponse = json_decode($response, true);
            $message = isset($decodedResponse['message']) ? $decodedResponse['message'] : 'Unknown error';
            throw new \Exception("API request failed with HTTP code $httpCode and message: $message");
        }

        return json_decode($response, true);
    }
}