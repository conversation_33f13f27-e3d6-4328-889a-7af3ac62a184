[{"name": "<PERSON>", "iconFile": "knight", "highresImageFilename": "image/chr/knight.png", "unlockArena": "TrainingCamp", "rarity": "Common", "tribe": "Kingdom", "manaCost": 3, "tid": "TID_SPELL_KNIGHT", "tidInfo": "TID_SPELL_INFO_KNIGHT", "id": 26000000, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Knight_EV1", "iconFile": "knight_evolution_card", "highresImageFilename": "image/chr_evolution/knight_evolution_dl.png", "unlockArena": "TrainingCamp", "rarity": "Common", "tribe": "Kingdom", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_KNIGHT", "tidInfo": "TID_SPELL_INFO_EVO_KNIGHT", "source": "spells_evolved", "summonCharacterData": {"name": "Knight_EV1", "base": "<PERSON>", "source": "characters_evo", "hitpoints": 690, "sightRange": 5500, "range": 1200, "loadTime": 700, "buffWhenNotAttackingData": {"name": "Knight_Fortify_EV1", "rarity": "Common", "damageReduction": 60, "source": "character_buffs_evo"}}}, "summonCharacterData": {"name": "<PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 690, "hitSpeed": 1200, "loadTime": 700, "damage": 79, "range": 1200, "collisionRadius": 500, "tid": "TID_SPELL_KNIGHT", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "<PERSON>"}, {"name": "<PERSON>", "iconFile": "archer", "highresImageFilename": "image/chr/archers.png", "unlockArena": "TrainingCamp", "rarity": "Common", "tribe": "Village", "manaCost": 3, "summonNumber": 2, "summonDeployDelay": 100, "tid": "TID_SPELL_ARCHERS", "tidInfo": "TID_SPELL_INFO_ARCHERS", "id": 26000001, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Archer_EV1", "iconFile": "archer_evolution_card", "highresImageFilename": "image/chr_evolution/archer_evolution_dl.png", "unlockArena": "TrainingCamp", "rarity": "Common", "tribe": "Village", "manaCost": 3, "summonNumber": 2, "summonDeployDelay": 100, "tid": "TID_SPELL_ARCHERS", "tidInfo": "TID_SPELL_INFO_EVO_ARCHERS", "source": "spells_evolved", "summonCharacterData": {"name": "Archer_EV1", "base": "<PERSON>", "sightRange": 6600, "range": 6000, "specialAttackRangeForStats": 4500, "source": "characters_evo", "hitpoints": 119, "loadTime": 400, "specialAttackRangeForStatsTid": "TID_SPELL_EVO_ARCHER_SPECIAL_RANGE", "projectileData": {"name": "Archer_EV1_Arrow", "base": "ArcherArrow", "source": "projectiles_evo", "damage": 42, "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "projectile2Data": {"name": "Archer_EV1_ArrowDoubleDamage", "base": "ArcherArrow", "damage": 84, "source": "projectiles_evo", "tid": "TID_SPELL_EVO_ARCHER_SPECIAL_DAMAGE"}}}, "summonCharacterData": {"name": "<PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 119, "hitSpeed": 900, "loadTime": 400, "range": 5000, "collisionRadius": 500, "tid": "TID_CHARACTER_ARCHER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "ArcherArrow", "rarity": "Common", "speed": 600, "damage": 42, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Archers"}, {"name": "Goblins", "iconFile": "goblins", "highresImageFilename": "image/chr/goblins.png", "unlockArena": "Arena1", "rarity": "Common", "tribe": "Goblin", "manaCost": 2, "summonNumber": 4, "summonRadius": 700, "summonDeployDelay": 200, "tid": "TID_SPELL_GOBLINS", "tidInfo": "TID_SPELL_INFO_GOBLINS", "id": 26000002, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Goblin_Stab", "rarity": "Common", "sightRange": 5500, "deployTime": 1200, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}, "englishName": "Goblins"}, {"name": "Giant", "iconFile": "giant", "highresImageFilename": "image/chr/giant.png", "unlockArena": "TrainingCamp", "rarity": "Rare", "tribe": "Village", "manaCost": 5, "tid": "TID_SPELL_GIANT", "tidInfo": "TID_SPELL_INFO_GIANT", "id": 26000003, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Giant", "rarity": "Rare", "sightRange": 7500, "deployTime": 1000, "speed": 45, "hitpoints": 1930, "hitSpeed": 1500, "loadTime": 1000, "damage": 120, "range": 1200, "collisionRadius": 750, "tid": "TID_SPELL_GIANT", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2"}, "englishName": "Giant"}, {"name": "<PERSON>ek<PERSON>", "iconFile": "pekka", "highresImageFilename": "image/chr/pekka_dl.png", "unlockArena": "Arena6", "rarity": "Epic", "tribe": "Beast", "manaCost": 7, "tid": "TID_SPELL_PEKKA", "tidInfo": "TID_SPELL_INFO_PEKKA", "id": 26000004, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"healthBar": "Medium_EV1", "onKilledDoneAction": "PekkaEV1_Heal", "customSpawnFilter": "filter_card_evolution", "visualActions": ["PekkaEvoVisual"], "clonedVersion": "<PERSON>ek<PERSON>", "fileName": "sc/chr_pekka_evolution.sc", "blueExportName": "pekka_evolution", "redExportName": "pekka_evolution_red", "scale": 75, "shadowScaleX": 85, "shadowScaleY": 80, "shadowY": 20, "shadowSkew": 25, "deathEffect": "Death_card_evolution_M", "spawnEffect": "Deploy_Pekka_Evolution", "deployBaseAnimExportName": "filter_deploy_unit_legendary", "moveEffect": "<PERSON><PERSON><PERSON>_Evo_steps", "damageEffect": "<PERSON><PERSON><PERSON>_Evo_hit", "attackStartEffect": "<PERSON><PERSON><PERSON>_Evo_attack_start", "tempResurrect": true, "resurrectGainChargeEffect": "pekka_evo_gain_soul_glow", "resurrectChargeFilter": "skeleton_king_charge_souls", "resurrectFlyingEffect": "pekka_evo_butterfly_explosion", "resurrectParameters": [0, 2000, 500, 500, 5000, 5000, 900, 10000, 200], "statsTags": {"damage": "damage", "hitpoints": "hitpoints", "hitSpeed": "hit_speed", "targets": "targets", "speed": "speed", "range": "range"}, "name": "Pekka_EV1", "source": "ext", "hitpoints": 2350, "sightRange": 5000, "range": 1600, "loadTime": 1300, "hitSpeed": 1800, "baseData": {"name": "<PERSON>ek<PERSON>", "rarity": "Epic", "sightRange": 5000, "deployTime": 1000, "speed": 45, "hitpoints": 2350, "hitSpeed": 1800, "loadTime": 1300, "damage": 510, "range": 1600, "collisionRadius": 750, "tid": "TID_SPELL_PEKKA", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_2"}}, "summonCharacterData": {"name": "<PERSON>ek<PERSON>", "rarity": "Epic", "sightRange": 5000, "deployTime": 1000, "speed": 45, "hitpoints": 2350, "hitSpeed": 1800, "loadTime": 1300, "damage": 510, "range": 1600, "collisionRadius": 750, "tid": "TID_SPELL_PEKKA", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_2"}, "englishName": "P.E.K.K.A"}, {"name": "Minions", "iconFile": "minion", "highresImageFilename": "image/chr/minions.png", "unlockArena": "TrainingCamp", "rarity": "Common", "manaCost": 3, "summonNumber": 3, "summonDeployDelay": 100, "tid": "TID_SPELL_MINIONS", "tidInfo": "TID_SPELL_INFO_MINIONS", "id": 26000005, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Minion", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 90, "hitSpeed": 1000, "loadTime": 500, "range": 1600, "collisionRadius": 500, "tid": "TID_CHARACTER_MINION", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_4", "projectileData": {"name": "MinionSpit", "rarity": "Common", "speed": 1000, "damage": 46, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Minions"}, {"name": "Balloon", "iconFile": "chr_balloon", "highresImageFilename": "image/chr/balloon_dl.png", "unlockArena": "Arena7", "rarity": "Epic", "manaCost": 5, "tid": "TID_SPELL_BALLOON", "tidInfo": "TID_SPELL_INFO_BALLOON", "id": 26000006, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Balloon", "rarity": "Epic", "sightRange": 7700, "deployTime": 1000, "speed": 60, "hitpoints": 1050, "hitSpeed": 2000, "loadTime": 1800, "damage": 400, "range": 100, "collisionRadius": 500, "tid": "TID_SPELL_BALLOON", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "deathSpawnCharacterData": {"name": "BalloonBomb", "rarity": "Epic", "deployTime": 3000, "deathDamage": 150, "collisionRadius": 450, "tid": "TID_BUILDING_GIANT_SKELETON_BOMB", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Balloon"}, {"name": "Witch", "iconFile": "witch", "highresImageFilename": "image/chr/witch_dl.png", "unlockArena": "Arena6", "rarity": "Epic", "manaCost": 5, "tid": "TID_SPELL_WITCH", "tidInfo": "TID_SPELL_INFO_WITCH", "id": 26000007, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Witch", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 524, "hitSpeed": 1100, "loadTime": 400, "range": 5500, "collisionRadius": 500, "spawnNumber": 4, "spawnPauseTime": 7000, "tid": "TID_SPELL_WITCH", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "WitchProjectile", "rarity": "Epic", "speed": 600, "damage": 84, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "spawnCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}, "englishName": "Witch"}, {"name": "Barbarians", "iconFile": "barbarian_card", "highresImageFilename": "image/chr/barbarians_dl.png", "unlockArena": "Arena3", "rarity": "Common", "manaCost": 5, "summonNumber": 5, "summonRadius": 700, "summonDeployDelay": 100, "tid": "TID_SPELL_BARBARIANS", "tidInfo": "TID_SPELL_INFO_BARBARIANS", "id": 26000008, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Barbarians_EV1", "iconFile": "barbarian_evolution_card", "highresImageFilename": "image/chr_evolution/barbarian_evolution_dl.png", "unlockArena": "Arena3", "rarity": "Common", "manaCost": 5, "summonNumber": 5, "summonRadius": 700, "summonDeployDelay": 100, "tid": "TID_SPELL_BARBARIANS", "tidInfo": "TID_SPELL_INFO_EVO_BARBARIAN", "source": "spells_evolved", "summonCharacterData": {"name": "Barbarian_EV1", "base": "Barbarian", "hitpoints": 288, "buffAfterHitsTime": [3000], "source": "characters_evo", "sightRange": 5500, "range": 700, "loadTime": 900, "buffAfterHitsData": [{"name": "Barbarian_EVO_Rage", "rarity": "Common", "hitSpeedMultiplier": 135, "speedMultiplier": 135, "source": "character_buffs_evo"}]}}, "summonCharacterData": {"name": "Barbarian", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 262, "hitSpeed": 1300, "loadTime": 900, "damage": 75, "range": 700, "collisionRadius": 500, "tid": "TID_CHARACTER_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Barbarians"}, {"name": "Golem", "iconFile": "chr_golem", "highresImageFilename": "image/chr/golem_dl.png", "unlockArena": "Arena_L", "rarity": "Epic", "manaCost": 8, "tid": "TID_SPELL_GOLEM", "tidInfo": "TID_SPELL_INFO_GOLEM", "id": 26000009, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Golem", "rarity": "Epic", "sightRange": 7000, "deployTime": 3000, "speed": 45, "hitpoints": 3200, "hitSpeed": 2500, "loadTime": 1500, "damage": 195, "range": 750, "deathDamage": 140, "collisionRadius": 750, "deathSpawnCount": 2, "tid": "TID_SPELL_GOLEM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "deathSpawnCharacterData": {"name": "Golemite", "rarity": "Epic", "sightRange": 7000, "deployTime": 1000, "speed": 45, "hitpoints": 650, "hitSpeed": 2500, "loadTime": 1500, "damage": 31, "range": 250, "deathDamage": 62, "collisionRadius": 500, "tid": "TID_CHARACTER_SMALL_GOLEM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2"}}, "englishName": "Golem"}, {"name": "Skeletons", "iconFile": "skeletons_card", "highresImageFilename": "image/chr/skeletons_dl.png", "unlockArena": "Arena2", "rarity": "Common", "manaCost": 1, "summonNumber": 3, "summonRadius": 700, "tid": "TID_SPELL_SKELETONS", "tidInfo": "TID_SPELL_INFO_SKELETONS", "id": 26000010, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Skeletons_EV1", "iconFile": "skeletons_evolution_card", "highresImageFilename": "image/chr_evolution/skeleton_evolution_dl.png", "unlockArena": "Arena2", "rarity": "Common", "manaCost": 1, "summonNumber": 3, "summonRadius": 700, "tid": "TID_SPELL_SKELETONS", "tidInfo": "TID_SPELL_INFO_EVO_SKELETONS", "source": "spells_evolved", "summonCharacterData": {"name": "Skeleton_EV1", "base": "Skeleton", "buffAfterHitsTime": [500], "groupMaxSize": 8, "source": "characters_evo", "hitpoints": 32, "sightRange": 5500, "range": 500, "loadTime": 500, "buffAfterHitsData": [{"name": "SkeletonDuplication_EV1", "rarity": "Common", "spawnNumber": 1, "spawnInterval": 50, "source": "character_buffs_evo"}]}}, "summonCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "englishName": "Skeletons"}, {"name": "Valkyrie", "iconFile": "valkyrie", "highresImageFilename": "image/chr/valkyrie_dl.png", "unlockArena": "Arena2", "rarity": "Rare", "manaCost": 4, "tid": "TID_SPELL_VALKYRIE", "tidInfo": "TID_SPELL_INFO_VALKYRIE", "id": 26000011, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Valkyrie_EV1", "iconFile": "valkyrie_evolution_card", "highresImageFilename": "image/chr_evolution/valkyrie_evolution_dl.png", "unlockArena": "Arena2", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_VALKYRIE", "tidInfo": "TID_SPELL_INFO_EVO_VALKYRIE", "source": "spells_evolved", "summonCharacterData": {"name": "Valkyrie_EV1", "base": "Valkyrie", "hitpoints": 990, "source": "characters_evo", "sightRange": 5500, "range": 1200, "loadTime": 1400, "onAttackActionData": {"name": "Valkyrie_EV1_Tornado", "source": "actions", "nextAction.spawnData": "Valkyrie_NotPushed_BUF", "spawnDataData": {"name": "Valkyrie_MiniTornado_EV1", "base": "Tornado", "lifeDuration": 500, "radius": 5500, "source": "area_effect_objects_evo", "hitSpeed": 50, "buffTime": 500, "tidRadius": "TID_VALKEVO_TORNADO_RADIUS", "tidDuration": "TID_VALKEVO_TORNADO_DURATION", "tidDamage": "TID_VALKEVO_TORNADO_DAMAGE", "buffData": {"name": "Valkyrie_MiniTornado_EV1", "base": "Tornado", "rarity": "Rare", "attractPercentage": 300, "damagePerSecond": 100, "hitFrequency": 400, "source": "character_buffs_evo", "crownTowerDamagePercent": -83, "tidDamage": "TID_VALKEVO_TORNADO_DAMAGE"}}}}}, "summonCharacterData": {"name": "Valkyrie", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 900, "hitSpeed": 1500, "loadTime": 1400, "damage": 126, "range": 1200, "areaDamageRadius": 2000, "collisionRadius": 500, "tid": "TID_CHARACTER_VALKYRIE", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Valkyrie"}, {"name": "SkeletonArmy", "iconFile": "skeleton_horde", "highresImageFilename": "image/chr/skeleton_army_dl.png", "unlockArena": "Arena6", "rarity": "Epic", "manaCost": 3, "summonNumber": 15, "summonCharacterLevelIndex": 5, "tid": "TID_SPELL_SKELETON_HORDE", "tidInfo": "TID_SPELL_INFO_SKELETON_HORDE", "id": 26000012, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "englishName": "Skeleton Army"}, {"name": "Bomber", "iconFile": "bomber", "highresImageFilename": "image/chr/bomber_dl.png", "unlockArena": "Arena2", "rarity": "Common", "manaCost": 2, "tid": "TID_SPELL_BOMBER", "tidInfo": "TID_SPELL_INFO_BOMBER", "id": 26000013, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Bomber_EV1", "iconFile": "bomber_evolution_card", "highresImageFilename": "image/chr_evolution/bomber_evolution_dl.png", "unlockArena": "Arena2", "rarity": "Common", "manaCost": 2, "summonNumber": 1, "tid": "TID_SPELL_BOMBER", "tidInfo": "TID_SPELL_INFO_EVO_BOMBER", "source": "spells_evolved", "summonCharacterData": {"name": "Bomber_EV1", "base": "Bomber", "source": "characters_evo", "hitpoints": 130, "sightRange": 5500, "range": 4500, "loadTime": 1600, "projectileData": {"name": "BombSkeletonProjectile_EV1", "damage": 88, "spawnChain": 2, "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnProjectileData": {"name": "BombSkeletonProjectile_2_EV1", "base": "BombSkeletonProjectile_EV1", "source": "projectiles_evo", "damage": 88, "spawnChain": 1, "tid": "TID_SPELL_ATTRIBUTE_BOUNCE_DAMAGE"}}}}, "summonCharacterData": {"name": "Bomber", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 130, "hitSpeed": 1800, "loadTime": 1600, "range": 4500, "collisionRadius": 500, "tid": "TID_SPELL_BOMBER", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "BombSkeletonProjectile", "rarity": "Common", "speed": 400, "damage": 88, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}}, "englishName": "Bomber"}, {"name": "Musketeer", "iconFile": "musketeer", "highresImageFilename": "image/chr/musketeer_dl.png", "unlockArena": "TrainingCamp", "rarity": "Rare", "manaCost": 4, "tid": "TID_SPELL_MUSKETEER", "tidInfo": "TID_SPELL_INFO_MUSKETEER", "id": 26000014, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"spawnNumber": 1, "clonedVersion": "Musketeer", "attackSequenceMode": "None", "attackSequence": [0, 1], "attackStartEffect2": "evo_musketeer_area_hit", "tryToFinishAttackAnimation": true, "statsTags": {"hitpoints": "hitpoints", "damage": "ranged_damage", "hitSpeed": "hit_speed", "targets": "targets", "speed": "speed", "range": "range"}, "fileName": "sc/chr_musketeer_evolution.sc", "blueExportName": "chr_musketeer_evo", "redExportName": "chr_musketeer_evo_red", "scale": 74, "spawnEffect": "evo_musketeer_deploy", "deployBaseAnimExportName": "filter_deploy_unit_legendary", "moveEffect": "evo_musketeer_steps", "deathEffect": "evo_musketeer_die", "damageEffect": "evo_musketeer_hit", "projectileEffect": "evo_musketeer_attack", "attackSequenceList": [{"projectile": "Musketeer_EV1_normal_projectile"}, {"projectile": "Musketeer_EV1_snipe_projectile", "customRange": 30000}], "name": "Musketeer_EV1", "source": "ext", "hitpoints": 340, "sightRange": 6000, "range": 6000, "loadTime": 200, "hitSpeed": 1000, "baseData": {"name": "Musketeer", "rarity": "Rare", "sightRange": 6000, "deployTime": 1000, "speed": 60, "hitpoints": 340, "hitSpeed": 1000, "loadTime": 200, "range": 6000, "collisionRadius": 500, "tid": "TID_CHARACTER_MUSKETEER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "MusketeerProjectile", "rarity": "Rare", "speed": 1000, "damage": 103, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "onStartingActionData": {"name": "Musketeer_EV1_snipe_targeting_group", "source": "actions", "subActionsData": [{"name": "Musketeer_EV1_snipe_deploy_effect", "source": "actions"}, {"name": "Musketeer_EV1_snipe_targeting", "source": "actions"}]}}, "summonCharacterData": {"name": "Musketeer", "rarity": "Rare", "sightRange": 6000, "deployTime": 1000, "speed": 60, "hitpoints": 340, "hitSpeed": 1000, "loadTime": 200, "range": 6000, "collisionRadius": 500, "tid": "TID_CHARACTER_MUSKETEER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "MusketeerProjectile", "rarity": "Rare", "speed": 1000, "damage": 103, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Musketeer"}, {"name": "Baby<PERSON><PERSON><PERSON>", "iconFile": "baby_dragon", "highresImageFilename": "image/chr/baby_dragon_dl.png", "unlockArena": "Arena6", "rarity": "Epic", "manaCost": 4, "tid": "TID_SPELL_BABY_DRAGON", "tidInfo": "TID_SPELL_INFO_BABY_DRAGON", "id": 26000015, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Baby<PERSON><PERSON><PERSON>", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 720, "hitSpeed": 1500, "loadTime": 1200, "range": 3500, "collisionRadius": 500, "tid": "TID_SPELL_BABY_DRAGON", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_4", "projectileData": {"name": "BabyDragonProjectile", "rarity": "Epic", "speed": 500, "damage": 100, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Baby Dragon"}, {"name": "Prince", "iconFile": "prince", "highresImageFilename": "image/chr/prince_dl.png", "unlockArena": "Arena7", "rarity": "Epic", "tribe": "Magic", "manaCost": 5, "tid": "TID_SPELL_PRINCE", "tidInfo": "TID_SPELL_INFO_PRINCE", "id": 26000016, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Prince", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "chargeRange": 200, "speed": 60, "hitpoints": 1200, "hitSpeed": 1400, "loadTime": 900, "damage": 245, "damageSpecial": 490, "range": 1600, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_SPELL_PRINCE", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Prince"}, {"name": "<PERSON>", "iconFile": "wizard", "highresImageFilename": "image/chr/wizard_dl.png", "unlockArena": "Arena4", "rarity": "Rare", "manaCost": 5, "tid": "TID_SPELL_WIZARD", "tidInfo": "TID_SPELL_INFO_WIZARD", "id": 26000017, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Wizard_EV1", "iconFile": "wizard_evolution_card", "highresImageFilename": "image/chr_evolution/wizard_evolution_dl.png", "unlockArena": "Arena_L10", "rarity": "Rare", "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_WIZARD", "tidInfo": "TID_SPELL_INFO_EVO_WIZARD", "source": "spells_evolved", "summonCharacterData": {"name": "Wizard_EV1", "base": "<PERSON>", "shieldHitpoints": 90, "source": "characters_evo", "hitpoints": 356, "sightRange": 5500, "range": 5500, "loadTime": 1000, "onStartingActionData": {"name": "Wizard_EV1_ShieldON", "source": "actions", "subActionsData": [{"name": "Wizard_EV1_ShieldVFX", "spawnTime": 999999, "source": "actions", "spawnDataData": {"name": "Wizard_EV1_ShieldVFX", "rarity": "Rare", "source": "character_buffs_evo"}}]}, "shieldLostActionData": {"name": "Wizard_EV1_ShieldLost", "source": "actions", "subActionsData": [{"name": "Wizard_EV1_ShieldLostAoE", "source": "actions", "spawnDataData": {"name": "Wizard_EV1_ShieldLostExplosion", "rarity": "Rare", "lifeDuration": 1, "radius": 3000, "damage": 109, "source": "area_effect_objects_evo", "tidDamage": "TID_SHIELD_EXPLOSION_DAMAGE"}}]}}}, "summonCharacterData": {"name": "<PERSON>", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 356, "hitSpeed": 1400, "loadTime": 1000, "range": 5500, "collisionRadius": 500, "tid": "TID_SPELL_WIZARD", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "chr_wizardProjectile", "rarity": "Rare", "speed": 600, "damage": 133, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "<PERSON>"}, {"name": "MiniPekka", "iconFile": "minipekka", "highresImageFilename": "image/chr/mini_pekka.png", "unlockArena": "TrainingCamp", "rarity": "Rare", "manaCost": 4, "tid": "TID_SPELL_MINIPEKKA", "tidInfo": "TID_SPELL_INFO_MINIPEKKA", "id": 26000018, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "MiniPekka", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 642, "hitSpeed": 1600, "loadTime": 1100, "damage": 356, "range": 800, "collisionRadius": 450, "tid": "TID_SPELL_MINIPEKKA", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "englishName": "Mini P.E.K.K.A"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iconFile": "goblin_archer", "highresImageFilename": "image/chr/spear_goblins.png", "unlockArena": "Arena1", "rarity": "Common", "manaCost": 2, "summonNumber": 3, "summonDeployDelay": 100, "tid": "TID_SPELL_SPEAR_GOBLINS", "tidInfo": "TID_SPELL_INFO_SPEAR_GOBLINS", "id": 26000019, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Spear Goblins"}, {"name": "GiantSkeleton", "iconFile": "giant_skeleton", "highresImageFilename": "image/chr/giant_skeleton_dl.png", "unlockArena": "Arena8", "rarity": "Epic", "manaCost": 6, "summonNumber": 1, "tid": "TID_SPELL_GIANT_SKELETON", "tidInfo": "TID_SPELL_INFO_GIANT_SKELETON", "id": 26000020, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "GiantSkeleton", "rarity": "Epic", "sightRange": 5000, "deployTime": 1000, "speed": 60, "hitpoints": 2260, "hitSpeed": 1400, "loadTime": 1100, "damage": 167, "range": 800, "collisionRadius": 1000, "deathSpawnCount": 1, "tid": "TID_SPELL_GIANT_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "deathSpawnCharacterData": {"name": "GiantSkeletonBomb", "rarity": "Epic", "deployTime": 3000, "crownTowerDamagePercent": 100, "deathDamage": 334, "collisionRadius": 450, "tid": "TID_BUILDING_GIANT_SKELETON_BOMB", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Giant Skeleton"}, {"name": "HogRider", "iconFile": "hog_rider", "highresImageFilename": "image/chr/hog_rider_dl.png", "unlockArena": "Arena5", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_HOG_RIDER", "tidInfo": "TID_SPELL_INFO_HOG_RIDER", "id": 26000021, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "HogRider", "rarity": "Rare", "sightRange": 9500, "deployTime": 1000, "speed": 120, "hitpoints": 800, "hitSpeed": 1600, "loadTime": 1000, "damage": 150, "range": 800, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_SPELL_HOG_RIDER", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5"}, "englishName": "Hog Rider"}, {"name": "MinionHorde", "iconFile": "minion_horde", "highresImageFilename": "image/chr/minions_horde_dl.png", "unlockArena": "Arena_L", "rarity": "Common", "manaCost": 5, "summonNumber": 6, "summonRadius": 600, "summonDeployDelay": 100, "tid": "TID_SPELL_MINION_HORDE", "tidInfo": "TID_SPELL_INFO_MINION_HORDE", "id": 26000022, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Minion", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 90, "hitSpeed": 1000, "loadTime": 500, "range": 1600, "collisionRadius": 500, "tid": "TID_CHARACTER_MINION", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_4", "projectileData": {"name": "MinionSpit", "rarity": "Common", "speed": 1000, "damage": 46, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "<PERSON><PERSON>"}, {"name": "IceWizard", "iconFile": "ice_wizard", "highresImageFilename": "image/chr/ice_wizard_dl.png", "unlockArena": "Arena_T", "rarity": "Legendary", "manaCost": 3, "radius": 3000, "tid": "TID_SPELL_ICE_WIZARD", "tidInfo": "TID_SPELL_INFO_ICE_WIZARD", "id": 26000023, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "IceWizard", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 569, "hitSpeed": 1700, "loadTime": 1200, "range": 5500, "collisionRadius": 500, "tid": "TID_SPELL_ICE_WIZARD", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "ice_wizardProjectile", "rarity": "Legendary", "speed": 700, "damage": 75, "radius": 1500, "buffTime": 2500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "targetBuffData": {"name": "IceWizardSlowDown", "rarity": "Common", "tid": "TID_SPELL_ATTRIBUTE_SLOWDOWN", "hitSpeedMultiplier": -35, "speedMultiplier": -35, "spawnSpeedMultiplier": -35, "source": "character_buffs"}}, "spawnAreaObjectData": {"name": "IceWizardCold", "rarity": "Legendary", "lifeDuration": 1, "radius": 3000, "damage": 69, "crownTowerDamagePercent": -100, "buffTime": 1000, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "IceWizardCold", "rarity": "Common", "tid": "TID_SPELL_ICEWIZARD_SLOW", "hitSpeedMultiplier": -35, "speedMultiplier": -35, "spawnSpeedMultiplier": -35, "source": "character_buffs"}}}, "englishName": "Ice Wizard"}, {"name": "RoyalGiant", "iconFile": "royalgiant", "highresImageFilename": "image/chr/royal_giant_dl.png", "unlockArena": "Arena7", "rarity": "Common", "manaCost": 6, "tid": "TID_SPELL_ROYAL_GIANT", "tidInfo": "TID_SPELL_INFO_ROYAL_GIANT", "id": 26000024, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "RoyalGiant_EV1", "iconFile": "royalgiant_evolution_card", "highresImageFilename": "image/chr_evolution/royal_giant_evolution_dl.png", "unlockArena": "Arena7", "rarity": "Common", "manaCost": 6, "summonNumber": 1, "tid": "TID_SPELL_ROYAL_GIANT", "tidInfo": "TID_SPELL_INFO_EVO_ROYAL_GIANT", "source": "spells_evolved", "summonCharacterData": {"name": "RoyalGiant_EV1", "base": "RoyalGiant", "source": "characters_evo", "hitpoints": 1236, "sightRange": 7500, "range": 5000, "loadTime": 800, "projectileData": {"name": "RoyalGiantProjectile_EV1", "damage": 120, "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE"}, "onAttackActionData": {"name": "RoyalGiant_EV1_PushBack", "source": "actions", "spawnDataData": {"name": "EvoRoyalGiantPush_EV1", "rarity": "Common", "lifeDuration": 1, "radius": 3000, "damage": 32, "source": "area_effect_objects_evo", "tidDamage": "TID_SPELL_ATTRIBUTE_DMG_RECOIL"}}}}, "summonCharacterData": {"name": "RoyalGiant", "rarity": "Common", "sightRange": 7500, "deployTime": 1000, "speed": 45, "hitpoints": 1236, "hitSpeed": 1700, "loadTime": 800, "range": 5000, "collisionRadius": 750, "tid": "TID_SPELL_ROYAL_GIANT", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "projectileData": {"name": "RoyalGiantProjectile", "rarity": "Common", "speed": 1000, "damage": 120, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Royal Giant"}, {"name": "SkeletonWarriors", "iconFile": "skeleton_warriors", "highresImageFilename": "image/chr/guards_dl.png", "unlockArena": "Arena6", "rarity": "Epic", "manaCost": 3, "summonNumber": 3, "summonDeployDelay": 100, "tid": "TID_SPELL_SKELETON_WARRIORS", "tidInfo": "TID_SPELL_INFO_SKELETON_WARRIORS", "id": 26000025, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SkeletonWarrior", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 51, "hitSpeed": 1000, "loadTime": 600, "damage": 76, "range": 1600, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON_WARRIOR", "shieldHitpoints": 160, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "englishName": "Guards"}, {"name": "Princess", "iconFile": "princess", "highresImageFilename": "image/chr/princess_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 3, "tid": "TID_SPELL_PRINCESS", "tidInfo": "TID_SPELL_INFO_PRINCESS", "id": 26000026, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Princess", "rarity": "Legendary", "sightRange": 9500, "deployTime": 1000, "speed": 60, "hitpoints": 216, "hitSpeed": 3000, "loadTime": 2500, "multipleProjectiles": 5, "range": 9000, "areaDamageRadius": 2500, "collisionRadius": 500, "tid": "TID_CHARACTER_PRINCESS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "PrincessProjectileDeco", "rarity": "Legendary", "speed": 600, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "customFirstProjectileData": {"name": "PrincessProjectile", "rarity": "Legendary", "speed": 600, "damage": 140, "radius": 2000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Princess"}, {"name": "Dark<PERSON><PERSON><PERSON>", "iconFile": "dark_prince", "highresImageFilename": "image/chr/dark_prince_dl.png", "unlockArena": "Arena7", "rarity": "Epic", "manaCost": 4, "tid": "TID_SPELL_DARK_PRINCE", "tidInfo": "TID_SPELL_INFO_DARK_PRINCE", "id": 26000027, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Dark<PERSON><PERSON><PERSON>", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "chargeRange": 300, "speed": 60, "hitpoints": 750, "hitSpeed": 1300, "loadTime": 900, "damage": 155, "damageSpecial": 310, "range": 1200, "areaDamageRadius": 1100, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_CHARACTER_DARK_PRINCE", "shieldHitpoints": 150, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Dark Prince"}, {"name": "ThreeMusketeers", "iconFile": "three_musketeers", "highresImageFilename": "image/chr/three_musketeers_dl.png", "unlockArena": "Arena7", "rarity": "Rare", "manaCost": 9, "summonNumber": 3, "summonDeployDelay": 100, "tid": "TID_SPELL_THREE_MUSKETEERS", "tidInfo": "TID_SPELL_INFO_THREE_MUSKETEERS", "id": 26000028, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "ThreeMusketeer", "rarity": "Rare", "sightRange": 6000, "deployTime": 1000, "speed": 60, "hitpoints": 340, "hitSpeed": 1000, "loadTime": 200, "range": 6000, "collisionRadius": 500, "tid": "TID_CHARACTER_MUSKETEER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "MusketeerProjectile", "rarity": "Rare", "speed": 1000, "damage": 103, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Three Musketeers"}, {"name": "LavaHound", "iconFile": "lava_hound", "highresImageFilename": "image/chr/lava_hound_dl.png", "unlockArena": "Arena13", "rarity": "Legendary", "manaCost": 7, "tid": "TID_SPELL_LAVA_HOUND", "tidInfo": "TID_SPELL_INFO_LAVA_HOUND", "id": 26000029, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "LavaHound", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 45, "hitpoints": 2960, "hitSpeed": 1300, "loadTime": 300, "range": 3500, "collisionRadius": 750, "deathSpawnCount": 6, "tid": "TID_SPELL_LAVA_HOUND", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "projectileData": {"name": "LavaHoundProjectile", "rarity": "Legendary", "speed": 400, "damage": 45, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "deathSpawnCharacterData": {"name": "LavaPups", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 179, "hitSpeed": 1700, "loadTime": 700, "range": 1600, "collisionRadius": 450, "tid": "TID_CHARACTER_LAVA_PUPS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "LavaPupProjectile", "rarity": "Legendary", "speed": 500, "damage": 75, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}, "englishName": "<PERSON><PERSON>"}, {"name": "IceSpirits", "iconFile": "snow_spirits", "highresImageFilename": "image/chr/ice_spirit_dl.png", "unlockArena": "Arena8", "rarity": "Common", "manaCost": 1, "summonNumber": 1, "tid": "TID_SPELL_ICE_SPIRITS", "tidInfo": "TID_SPELL_INFO_ICE_SPIRITS", "id": 26000030, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "IceSpirits_EV1", "iconFile": "icespirit_evolution_card", "highresImageFilename": "image/chr_evolution/icespirit_evolution_dl.png", "unlockArena": "Arena8", "rarity": "Common", "manaCost": 1, "summonNumber": 1, "tid": "TID_SPELL_ICE_SPIRITS", "tidInfo": "TID_SPELL_INFO_EVO_ICE_SPIRITS", "source": "spells_evolved", "summonCharacterData": {"name": "IceSpirits_EV1", "base": "IceSpirits", "source": "characters_evo", "hitpoints": 90, "sightRange": 5500, "range": 2500, "loadTime": 100, "projectileData": {"name": "IceSpiritsProjectile_EV1", "damage": 64, "buffTime": 1200, "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnAreaEffectObjectData": {"name": "IceSpiritsAOE_EV1", "rarity": "Common", "lifeDuration": 3000, "radius": 2000, "hitSpeed": 3000, "damage": 64, "buffTime": 1300, "source": "area_effect_objects_evo", "tid": "TID_SPELL_EVO_ICE_SPIRIT_SPECIAL_DAMAGE", "buffData": {"name": "Freeze", "rarity": "Common", "tid": "TID_SPELL_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}, "targetBuffData": {"name": "Freeze", "rarity": "Common", "tid": "TID_SPELL_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}, "onHitTargetActionData": {"name": "IceSpirits_Hit_EV1", "spawnTime": 3000, "source": "actions", "spawnDataData": {"name": "IceSpirits_Target_EV1", "rarity": "Common", "tid": "TID_SPELL_RAGE", "source": "character_buffs"}}}}}, "summonCharacterData": {"name": "IceSpirits", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 90, "hitSpeed": 300, "loadTime": 100, "range": 2500, "collisionRadius": 400, "kamikaze": true, "tid": "TID_CHARACTER_ICE_SPIRITS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "IceSpiritsProjectile", "rarity": "Common", "speed": 400, "damage": 43, "radius": 1500, "buffTime": 1200, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "targetBuffData": {"name": "Freeze", "rarity": "Common", "tid": "TID_SPELL_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}, "englishName": "Ice Spirit"}, {"name": "FireSpirits", "iconFile": "fire_spirits", "unlockArena": "Arena4", "rarity": "Common", "manaCost": 1, "summonNumber": 1, "summonDeployDelay": 100, "tid": "TID_SPELL_FIRE_SPIRITS", "tidInfo": "TID_SPELL_INFO_FIRE_SPIRITS", "id": 26000031, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "FireSpirits", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 90, "hitSpeed": 300, "loadTime": 100, "range": 2000, "collisionRadius": 400, "kamikaze": true, "tid": "TID_CHARACTER_FIRE_SPIRITS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "FireSpiritsProjectile", "rarity": "Common", "speed": 400, "damage": 81, "radius": 2300, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Fire Spirit"}, {"name": "Miner", "iconFile": "miner", "highresImageFilename": "image/chr/miner_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 3, "tid": "TID_SPELL_MINER", "tidInfo": "TID_SPELL_INFO_MINER", "id": 26000032, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Miner", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 1000, "hitSpeed": 1200, "loadTime": 700, "damage": 160, "crownTowerDamagePercent": -75, "range": 1200, "collisionRadius": 500, "tid": "TID_SPELL_MINER", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "englishName": "Miner"}, {"name": "ZapMachine", "iconFile": "zapMachine", "highresImageFilename": "image/chr/sparky_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 6, "tid": "TID_SPELL_ZAPMACHINE", "tidInfo": "TID_SPELL_INFO_ZAPMACHINE", "id": 26000033, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "ZapMachine", "rarity": "Legendary", "sightRange": 5000, "deployTime": 1000, "speed": 45, "hitpoints": 1200, "hitSpeed": 4000, "loadTime": 3000, "range": 5000, "collisionRadius": 1000, "tid": "TID_SPELL_ZAPMACHINE", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_2", "projectileData": {"name": "ZapMachineProjectile", "rarity": "Legendary", "speed": 1400, "damage": 1100, "radius": 1800, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}}, "englishName": "Sparky"}, {"name": "Bowler", "iconFile": "bowler", "highresImageFilename": "image/chr/bowler_dl.png", "unlockArena": "Arena13", "rarity": "Epic", "manaCost": 5, "tid": "TID_SPELL_BOWLER", "tidInfo": "TID_SPELL_INFO_BOWLER", "id": 26000034, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Bowler", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 45, "hitpoints": 1300, "hitSpeed": 2500, "loadTime": 2000, "range": 4000, "collisionRadius": 750, "tid": "TID_SPELL_BOWLER", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_2", "projectileData": {"name": "BowlerProjectile", "rarity": "Epic", "speed": 170, "damage": 180, "pushback": 1000, "radius": 1800, "projectileRange": 7500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}}, "englishName": "Bowler"}, {"name": "RageBarbarian", "iconFile": "rage_barbarian", "highresImageFilename": "image/chr/lumberjack_dl.png", "unlockArena": "Arena14", "rarity": "Legendary", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_RAGE_BARBARIAN", "tidInfo": "TID_SPELL_INFO_RAGE_BARBARIAN", "id": 26000035, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "RageBarbarian", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 1060, "hitSpeed": 800, "loadTime": 400, "damage": 212, "range": 700, "collisionRadius": 500, "tid": "TID_CHARACTER_RAGE_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5", "deathSpawnCharacterData": {"name": "RageBarbarianBottle", "rarity": "Legendary", "deployTime": 500, "source": "buildings", "deathAreaEffectData": {"name": "BarbarianRage", "rarity": "Legendary", "lifeDuration": 5500, "radius": 3000, "hitSpeed": 300, "buffTime": 2000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "Rage", "rarity": "Epic", "tid": "TID_SPELL_RAGE", "hitSpeedMultiplier": 135, "speedMultiplier": 135, "spawnSpeedMultiplier": 135, "source": "character_buffs"}, "spawnAreaEffectObjectData": {"name": "BarbarianRageDamage", "rarity": "Legendary", "lifeDuration": 1, "radius": 3000, "damage": 160, "crownTowerDamagePercent": -70, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects"}}}}, "englishName": "Lumber<PERSON>"}, {"name": "BattleRam", "iconFile": "battle_ram", "highresImageFilename": "image/chr/battle_ram_dl.png", "unlockArena": "Arena3", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_BATTLE_RAM", "tidInfo": "TID_SPELL_INFO_BATTLE_RAM", "id": 26000036, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "BattleRam_EV1", "iconFile": "battle_ram_evolution_card", "highresImageFilename": "image/chr_evolution/battle_ram_evolution_dl.png", "unlockArena": "Arena_L10", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_BATTLE_RAM", "tidInfo": "TID_SPELL_INFO_EVO_BATTLE_RAM", "source": "spells_evolved", "summonCharacterData": {"name": "BattleRam_EV1", "base": "BattleRam", "source": "characters_evo", "hitpoints": 456, "sightRange": 5500, "range": 500, "loadTime": 50, "onStartChargingActionData": {"name": "BattleRam_EV1_PushBack", "pushBackStrength": 2500, "pushBackDamage": 100, "source": "actions"}}}, "summonCharacterData": {"name": "BattleRam", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "chargeRange": 300, "speed": 60, "hitpoints": 456, "hitSpeed": 400, "loadTime": 50, "damage": 135, "damageSpecial": 270, "range": 500, "collisionRadius": 750, "deathSpawnCount": 2, "kamikaze": true, "tid": "TID_SPELL_BATTLE_RAM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "deathSpawnCharacterData": {"name": "Barbarian", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 262, "hitSpeed": 1300, "loadTime": 900, "damage": 75, "range": 700, "collisionRadius": 500, "tid": "TID_CHARACTER_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}, "englishName": "Battle Ram"}, {"name": "InfernoDragon", "iconFile": "inferno_dragon", "highresImageFilename": "image/chr/inferno_dragon_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_INFERNO_DRAGON", "tidInfo": "TID_SPELL_INFO_INFERNO_DRAGON", "id": 26000037, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "InfernoDragon", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 1070, "hitSpeed": 400, "loadTime": 1200, "damage": 30, "range": 3500, "collisionRadius": 500, "tid": "TID_CHARACTER_INFERNO_DRAGON", "variableDamage2": 100, "variableDamage3": 350, "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Inferno Dragon"}, {"name": "IceGolemite", "iconFile": "icegolem", "highresImageFilename": "image/chr/ice_golem_dl.png", "unlockArena": "Arena8", "rarity": "Rare", "manaCost": 2, "summonNumber": 1, "tid": "TID_SPELL_ICEGOLEMITE", "tidInfo": "TID_SPELL_INFO_ICEGOLEMITE", "id": 26000038, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "IceGolemite", "rarity": "Rare", "sightRange": 7000, "deployTime": 1000, "speed": 45, "hitpoints": 565, "hitSpeed": 2500, "loadTime": 1500, "damage": 40, "range": 750, "deathDamage": 40, "collisionRadius": 700, "tid": "TID_CHARACTER_SMALL_GOLEM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "deathAreaEffectData": {"name": "FreezeIceGolemite", "rarity": "Rare", "lifeDuration": 1000, "radius": 2000, "buffTime": 2000, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "IceWizardSlowDown", "rarity": "Common", "tid": "TID_SPELL_ATTRIBUTE_SLOWDOWN", "hitSpeedMultiplier": -35, "speedMultiplier": -35, "spawnSpeedMultiplier": -35, "source": "character_buffs"}}}, "englishName": "Ice Golem"}, {"name": "MegaMinion", "iconFile": "mega_minion", "highresImageFilename": "image/chr/mega_minion_dl.png", "unlockArena": "Arena3", "rarity": "Rare", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_MEGAMINION", "tidInfo": "TID_SPELL_INFO_MEGAMINION", "id": 26000039, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "MegaMinion", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 395, "hitSpeed": 1500, "loadTime": 1100, "range": 1600, "collisionRadius": 600, "tid": "TID_CHARACTER_MEGAMINION", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "MegaMinionSpit", "rarity": "Rare", "speed": 1000, "damage": 147, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Mega Minion"}, {"name": "BlowdartGoblin", "iconFile": "blowdart_goblin", "highresImageFilename": "image/chr/dart_goblin_dl.png", "unlockArena": "Arena9", "rarity": "Rare", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_BLOWDART_GOBLIN", "tidInfo": "TID_SPELL_INFO_BLOWDART_GOBLIN", "id": 26000040, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"clonedVersion": "BlowdartGoblin", "projectile": "BlowdartGoblinEvoProjectile", "statsTags": {"damage": "damage", "hitpoints": "hitpoints", "speed": "speed", "hitSpeed": "hit_speed", "targets": "targets", "range": "range"}, "fileName": "sc/chr_goblin_blowdart_evolution.sc", "blueExportName": "<PERSON><PERSON><PERSON><PERSON>_Goblin_Evo", "redExportName": "<PERSON><PERSON><PERSON><PERSON>_Goblin_Evo_red", "scale": 65, "spawnEffect": "evo_blowdart_goblin_deploy", "moveEffect": "evo_blowdart_goblin_steps", "deathEffect": "Death_card_evolution_M", "attackStartEffect": "evo_blowdart_goblin_atk_start", "deployBaseAnimExportName": "filter_deploy_unit_legendary", "name": "BlowdartGoblin_EV1", "source": "ext", "hitpoints": 123, "sightRange": 7500, "range": 6500, "loadTime": 350, "hitSpeed": 700, "baseData": {"name": "BlowdartGoblin", "rarity": "Rare", "sightRange": 7500, "deployTime": 1000, "speed": 120, "hitpoints": 123, "hitSpeed": 700, "loadTime": 350, "range": 6500, "collisionRadius": 500, "tid": "TID_CHARACTER_BLOWDART_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "BlowdartGoblinProjectile", "rarity": "Rare", "speed": 800, "damage": 67, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "onStartingActionData": {"name": "B<PERSON><PERSON><PERSON>_goblin_evo_select_projectile", "source": "actions"}}, "summonCharacterData": {"name": "BlowdartGoblin", "rarity": "Rare", "sightRange": 7500, "deployTime": 1000, "speed": 120, "hitpoints": 123, "hitSpeed": 700, "loadTime": 350, "range": 6500, "collisionRadius": 500, "tid": "TID_CHARACTER_BLOWDART_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "BlowdartGoblinProjectile", "rarity": "Rare", "speed": 800, "damage": 67, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "<PERSON><PERSON> Go<PERSON>"}, {"name": "GoblinGang", "iconFile": "goblin_gang", "highresImageFilename": "image/chr/goblins_gang_dl.png", "unlockArena": "Arena9", "rarity": "Common", "manaCost": 3, "summonNumber": 3, "summonCharacterSecondCount": 3, "summonRadius": 1000, "summonDeployDelay": 100, "tid": "TID_SPELL_GOBLIN_GANG", "tidInfo": "TID_SPELL_INFO_GOBLIN_GANG", "id": 26000041, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}, "summonCharacterSecondData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Goblin Gang"}, {"name": "ElectroWizard", "iconFile": "electro_wizard", "highresImageFilename": "image/chr/electro_wizard_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 4, "summonNumber": 1, "radius": 3000, "tid": "TID_SPELL_ELECTRO_WIZARD", "tidInfo": "TID_SPELL_INFO_ELECTRO_WIZARD", "id": 26000042, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "ElectroWizard", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 590, "hitSpeed": 1800, "loadTime": 1200, "damage": 91, "multipleTargets": 2, "range": 5000, "buffOnDamageTime": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_ELECTRO_WIZARD", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_4", "spawnAreaObjectData": {"name": "ElectroWizardZap", "rarity": "Legendary", "lifeDuration": 1, "radius": 3000, "damage": 159, "crownTowerDamagePercent": -100, "buffTime": 500, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}, "buffOnDamageData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}, "englishName": "Electro Wizard"}, {"name": "AngryBarbarians", "iconFile": "angry_barbarian", "highresImageFilename": "image/chr/elite_barbarians_dl.png", "unlockArena": "Arena_L", "rarity": "Common", "manaCost": 6, "summonNumber": 2, "summonRadius": 700, "summonDeployDelay": 100, "tid": "TID_SPELL_ANGRY_BARBARIANS", "tidInfo": "TID_SPELL_INFO_ANGRY_BARBARIANS", "id": 26000043, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "AngryBarbarian", "rarity": "Common", "sightRange": 6000, "deployTime": 1000, "speed": 90, "hitpoints": 524, "hitSpeed": 1400, "loadTime": 900, "damage": 150, "range": 1200, "collisionRadius": 500, "tid": "TID_CHARACTER_ANGRY_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "englishName": "Elite Barbarians"}, {"name": "<PERSON>", "iconFile": "hunter", "highresImageFilename": "image/chr/hunter_dl.png", "unlockArena": "Arena_L", "rarity": "Epic", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_HUNTER", "tidInfo": "TID_SPELL_INFO_HUNTER", "id": 26000044, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON>", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 553, "hitSpeed": 2200, "loadTime": 1400, "multipleProjectiles": 10, "range": 4000, "areaDamageRadius": 70, "collisionRadius": 600, "tid": "TID_SPELL_HUNTER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "HunterProjectile", "rarity": "Epic", "speed": 550, "damage": 53, "projectileRange": 6500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "customFirstProjectileData": {"name": "HunterProjectile", "rarity": "Epic", "speed": 550, "damage": 53, "projectileRange": 6500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "<PERSON>"}, {"name": "AxeMan", "iconFile": "executioner", "highresImageFilename": "image/chr/executioner_dl.png", "unlockArena": "Arena14", "rarity": "Epic", "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_AXEMAN", "tidInfo": "TID_SPELL_INFO_AXEMAN", "id": 26000045, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "AxeMan", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 800, "hitSpeed": 900, "loadTime": 400, "range": 4500, "collisionRadius": 600, "tid": "TID_SPELL_AXEMAN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "AxeManProjectile", "rarity": "Epic", "speed": 550, "damage": 106, "radius": 1000, "projectileRange": 7500, "pingpongVisualTime": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Executioner"}, {"name": "Assassin", "iconFile": "bandit", "highresImageFilename": "image/chr/bandit_dl.png", "unlockArena": "Arena13", "rarity": "Legendary", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_ASSASSIN", "tidInfo": "TID_SPELL_INFO_ASSASSIN", "id": 26000046, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Assassin", "rarity": "Legendary", "sightRange": 6000, "deployTime": 1000, "speed": 90, "hitpoints": 750, "hitSpeed": 1000, "loadTime": 600, "damage": 160, "range": 750, "collisionRadius": 600, "dashDamage": 320, "dashMinRange": 3500, "dashMaxRange": 6000, "jumpSpeed": 500, "tid": "TID_SPELL_ASSASSIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "englishName": "Bandit"}, {"name": "RoyalRecruits", "iconFile": "royal_recruits", "highresImageFilename": "image/chr/royal_recruits_dl.png", "unlockArena": "Arena7", "rarity": "Common", "manaCost": 7, "summonNumber": 6, "summonRadius": 500, "summonDeployDelay": 100, "tid": "TID_SPELL_GUARD_BATTALION", "tidInfo": "TID_SPELL_INFO_GUARD_BATTALION", "id": 26000047, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "RoyalRecruits_EV1", "iconFile": "royal_recruit_evolution_card", "highresImageFilename": "image/chr_evolution/royal_recruit_evolution_dl.png", "unlockArena": "Arena7", "rarity": "Common", "manaCost": 7, "summonNumber": 6, "summonRadius": 500, "summonDeployDelay": 100, "tid": "TID_SPELL_GUARD_BATTALION", "tidInfo": "TID_SPELL_INFO_EVO_ROYAL_RECRUITS", "source": "spells_evolved", "summonCharacterData": {"name": "Recruit_EV1", "base": "Recruit", "chargeSpeedMultiplier": 200, "damageSpecial": 104, "source": "characters_evo", "hitpoints": 214, "sightRange": 5500, "range": 1600, "loadTime": 800, "shieldLostActionData": {"name": "Recruit_EV1_StartCharge", "spawnTime": 999999, "source": "actions", "spawnDataData": {"name": "RecruitsCharge_EV1", "rarity": "Common", "source": "character_buffs_evo"}}}}, "summonCharacterData": {"name": "Recruit", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 214, "hitSpeed": 1300, "loadTime": 800, "damage": 52, "range": 1600, "collisionRadius": 500, "tid": "TID_SPELL_GUARD_BATTALION", "shieldHitpoints": 94, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Royal Recruits"}, {"name": "<PERSON><PERSON><PERSON>", "iconFile": "nightwitch", "highresImageFilename": "image/chr/night_witch_dl.png", "unlockArena": "Arena14", "rarity": "Legendary", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_DARK_WITCH", "tidInfo": "TID_SPELL_INFO_DARK_WITCH", "id": 26000048, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 750, "hitSpeed": 1300, "loadTime": 550, "damage": 260, "range": 1600, "collisionRadius": 500, "spawnNumber": 2, "spawnPauseTime": 5000, "deathSpawnCount": 1, "tid": "TID_SPELL_DARK_WITCH", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "spawnCharacterData": {"name": "Bat", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 32, "hitSpeed": 1300, "loadTime": 700, "damage": 32, "range": 1200, "collisionRadius": 500, "tid": "TID_CHARACTER_BAT", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5"}, "deathSpawnCharacterData": {"name": "Bat", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 32, "hitSpeed": 1300, "loadTime": 700, "damage": 32, "range": 1200, "collisionRadius": 500, "tid": "TID_CHARACTER_BAT", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5"}}, "englishName": "Night Witch"}, {"name": "Bats", "iconFile": "bats", "highresImageFilename": "image/chr/bats_dl.png", "unlockArena": "Arena5", "rarity": "Common", "manaCost": 2, "summonNumber": 5, "summonRadius": 750, "summonDeployDelay": 100, "tid": "TID_SPELL_BATS", "tidInfo": "TID_SPELL_INFO_BATS", "id": 26000049, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Bats_EV1", "iconFile": "bats_evolution_card", "highresImageFilename": "image/chr_evolution/bats_evolution_dl.png", "unlockArena": "Arena5", "rarity": "Common", "manaCost": 2, "summonNumber": 5, "summonRadius": 750, "summonDeployDelay": 100, "tid": "TID_SPELL_BATS", "tidInfo": "TID_SPELL_INFO_EVO_BATS", "source": "spells_evolved", "summonCharacterData": {"name": "Bat_EV1", "base": "Bat", "hitpoints": 48, "buffAfterHitsTime": [1000], "source": "characters_evo", "sightRange": 5500, "range": 1200, "loadTime": 700, "buffAfterHitsData": [{"name": "BatsEV1_Heal", "rarity": "Common", "hitFrequency": 500, "healPerSecond": 30, "allowedOverHealPerc": 200, "source": "character_buffs_evo"}]}}, "summonCharacterData": {"name": "Bat", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 32, "hitSpeed": 1300, "loadTime": 700, "damage": 32, "range": 1200, "collisionRadius": 500, "tid": "TID_CHARACTER_BAT", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5"}, "englishName": "Bats"}, {"name": "Ghost", "iconFile": "ghost", "highresImageFilename": "image/chr/royal_ghost_dl.png", "unlockArena": "Arena_T", "rarity": "Legendary", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_GHOST", "tidInfo": "TID_SPELL_INFO_GHOST", "id": 26000050, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Ghost", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 1000, "hitSpeed": 1800, "loadTime": 1200, "damage": 216, "range": 1200, "areaDamageRadius": 1000, "collisionRadius": 600, "tid": "TID_SPELL_GHOST", "buffWhenNotAttackingTime": 1800, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4", "buffWhenNotAttackingData": {"name": "Invisibility", "rarity": "Legendary", "source": "character_buffs"}}, "englishName": "Royal Ghost"}, {"name": "RamRider", "iconFile": "ram_rider", "highresImageFilename": "image/chr/ram_rider_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 5, "tid": "TID_SPELL_RAM_RIDER", "tidInfo": "TID_SPELL_INFO_RAM_RIDER", "id": 26000051, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Ram", "rarity": "Legendary", "sightRange": 7500, "deployTime": 1000, "chargeRange": 200, "speed": 60, "hitpoints": 1402, "hitSpeed": 1800, "loadTime": 1200, "damage": 207, "damageSpecial": 414, "range": 800, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "spawnNumber": 1, "tid": "TID_SPELL_HOG_RIDER", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "spawnCharacterData": {"name": "RamRider", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 490, "hitSpeed": 1100, "loadTime": 700, "range": 5500, "collisionRadius": 600, "tid": "TID_CHARACTER_RAMRIDER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "RamRiderBola", "rarity": "Legendary", "speed": 600, "damage": 86, "buffTime": 2000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "targetBuffData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Legendary", "tid": "TID_SPELL_ATTRIBUTE_SNARE", "speedMultiplier": -70, "source": "character_buffs"}}}}, "englishName": "Ram Rider"}, {"name": "MiniSparkys", "iconFile": "zappies", "highresImageFilename": "image/chr/zappies_dl.png", "unlockArena": "Arena_L", "rarity": "Rare", "manaCost": 4, "summonNumber": 3, "summonRadius": 1000, "summonDeployDelay": 100, "tid": "TID_SPELL_MINI_ZAPMACHINE", "tidInfo": "TID_SPELL_INFO_MINI_ZAPMACHINE", "id": 26000052, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "MiniZapMachine", "rarity": "Rare", "sightRange": 5000, "deployTime": 1000, "speed": 60, "hitpoints": 250, "hitSpeed": 2100, "loadTime": 1000, "damage": 55, "range": 4500, "buffOnDamageTime": 500, "collisionRadius": 600, "tid": "TID_SPELL_MINI_ZAPMACHINE", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "buffOnDamageData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}, "englishName": "Zappies"}, {"name": "Rascals", "iconFile": "rascals", "highresImageFilename": "image/chr/rascals_dl.png", "unlockArena": "Arena13", "rarity": "Common", "manaCost": 5, "summonNumber": 1, "summonCharacterSecondCount": 2, "summonRadius": 3000, "tid": "TID_SPELL_HOODED_GANG", "tidInfo": "TID_SPELL_INFO_HOODED_GANG", "id": 26000053, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "RascalBoy", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 758, "hitSpeed": 1500, "loadTime": 1100, "damage": 52, "range": 800, "collisionRadius": 750, "tid": "TID_CHARACTER_HOODED_THUG", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "summonCharacterSecondData": {"name": "RascalGirl", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 102, "hitSpeed": 1000, "loadTime": 500, "range": 5000, "collisionRadius": 500, "tid": "TID_CHARACTER_HOODED_ARCHER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "RascalGirlProjectile", "rarity": "Common", "speed": 800, "damage": 52, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Rascals"}, {"name": "MovingCannon", "iconFile": "cannon_cart", "highresImageFilename": "image/chr/cannon_cart_dl.png", "unlockArena": "Arena_L1", "rarity": "Epic", "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_MOVING_CANNON", "tidInfo": "TID_SPELL_INFO_MOVING_CANNON", "id": 26000054, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "MovingCannon", "rarity": "Epic", "sightRange": 6000, "deployTime": 1000, "speed": 60, "hitpoints": 515, "hitSpeed": 900, "loadTime": 400, "range": 5500, "collisionRadius": 600, "deathSpawnCount": 1, "tid": "TID_SPELL_MOVING_CANNON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "MovingCannonProjectile", "rarity": "Epic", "speed": 1000, "damage": 133, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "deathSpawnCharacterData": {"name": "BrokenCannon", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "hitpoints": 515, "hitSpeed": 900, "loadTime": 400, "range": 5500, "lifeTime": 30000, "collisionRadius": 600, "tid": "TID_SPELL_<PERSON>OKEN_CANNON", "source": "buildings", "tidTarget": "TID_TARGETS_GROUND", "projectileData": {"name": "MovingCannonProjectile", "rarity": "Epic", "speed": 1000, "damage": 133, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}}, "englishName": "<PERSON>"}, {"name": "MegaKnight", "iconFile": "mega_knight", "highresImageFilename": "image/chr/mega_knight_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 7, "summonNumber": 1, "tid": "TID_SPELL_MEGAKNIGHT", "tidInfo": "TID_SPELL_INFO_MEGAKNIGHT", "id": 26000055, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "projectileData": {"name": "MegaKnightAppear", "rarity": "Legendary", "speed": 1000, "damage": 355, "pushback": 1000, "radius": 2200, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}, "evolvedSpellsData": {"fileName": "sc/chr_mega_knight_evolution.sc", "blueExportName": "megaknight_evolution", "redExportName": "megaknight_evolution_red", "customSpawnFilter": "filter_card_evolution", "deployBaseAnimExportName": "filter_deploy_unit_legendary", "scale": 60, "shadowScaleX": 85, "shadowScaleY": 80, "shadowY": 0, "shadowSkew": 25, "healthBar": "High_EV1", "clonedVersion": "MegaKnight", "tryToFinishAttackAnimation": true, "deathEffect": "mega_knight_evolution_die", "spawnEffect": "deploy_mega_knight_evolution", "damageEffect": "mega_knight_evo_hit", "moveEffect": "mega_knight_evo_steps", "attackStartEffect": "mega_knight_evo_attack_start", "dashStartEffect": "mega_knight_evo_dash_start", "landingEffect": "mega_knight_evo_dash_end", "dashEffect": "mega_knight_evo_dashing", "dashFilter": "filter_mega_knight_jump_evolution", "name": "MegaKnight_EV1", "source": "ext", "hitpoints": 3300, "sightRange": 5500, "range": 1200, "loadTime": 1200, "hitSpeed": 1700, "baseData": {"name": "MegaKnight", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 3300, "hitSpeed": 1700, "loadTime": 1200, "damage": 222, "range": 1200, "areaDamageRadius": 1300, "collisionRadius": 750, "jumpHeight": 3000, "dashDamage": 444, "dashMinRange": 3500, "dashMaxRange": 5000, "jumpSpeed": 250, "spawnInterval": 1, "spawnNumber": 1, "tid": "TID_SPELL_MEGAKNIGHT", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "onAttackActionData": {"pushBackStrength": 4000, "doFollowUpJump": false, "dashFollowUpMinRange": 3500, "dashFollowUpMaxRange": 5000, "name": "MegaKnight_EV1_uppercut", "source": "actions"}}, "summonCharacterData": {"name": "MegaKnight", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 3300, "hitSpeed": 1700, "loadTime": 1200, "damage": 222, "range": 1200, "areaDamageRadius": 1300, "collisionRadius": 750, "jumpHeight": 3000, "dashDamage": 444, "dashMinRange": 3500, "dashMaxRange": 5000, "jumpSpeed": 250, "spawnInterval": 1, "spawnNumber": 1, "tid": "TID_SPELL_MEGAKNIGHT", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Mega Knight"}, {"name": "SkeletonBalloon", "iconFile": "skeleton_balloon", "highresImageFilename": "image/chr/skeleton_barrel_dl.png", "unlockArena": "Arena9", "rarity": "Common", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_SKELETON_BALLOON", "tidInfo": "TID_SPELL_INFO_SKELETON_BALLOON", "id": 26000056, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SkeletonBalloon", "rarity": "Common", "sightRange": 7700, "deployTime": 1000, "speed": 90, "hitpoints": 208, "hitSpeed": 300, "loadTime": 200, "collisionRadius": 500, "deathSpawnCount": 1, "kamikaze": true, "tid": "TID_SPELL_BALLOON", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_4", "deathSpawnCharacterData": {"name": "SkeletonContainer", "rarity": "Common", "deployTime": 600, "deathDamage": 57, "deathSpawnCount": 7, "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "deathSpawnCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}}, "englishName": "Skeleton Barrel"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iconFile": "flying_machine", "highresImageFilename": "image/chr/flying_machine_dl.png", "unlockArena": "Arena5", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_DART_BARRELL", "tidInfo": "TID_SPELL_INFO_DART_BARRELL", "id": 26000057, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Rare", "sightRange": 6000, "deployTime": 1000, "speed": 90, "hitpoints": 290, "hitSpeed": 1100, "loadTime": 600, "range": 6000, "collisionRadius": 500, "tid": "TID_CHARACTER_BLOWDART_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_4", "projectileData": {"name": "DartBarrellProjectile", "rarity": "Rare", "speed": 800, "damage": 81, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Flying Machine"}, {"name": "Wallbreakers", "iconFile": "wallbreaker", "highresImageFilename": "image/chr/wall_breakers_dl.png", "unlockArena": "Arena_T", "rarity": "Epic", "manaCost": 2, "summonNumber": 2, "summonRadius": 750, "summonDeployDelay": 100, "tid": "TID_SPELL_WALLBREAKERS", "tidInfo": "TID_SPELL_INFO_WALLBREAKERS", "id": 26000058, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Wallbreakers_EV1", "iconFile": "wall_breaker_evolution_card", "highresImageFilename": "image/chr_evolution/wall_breaker_evolution_dl.png", "unlockArena": "Arena_T", "rarity": "Epic", "manaCost": 2, "summonNumber": 2, "summonRadius": 750, "summonDeployDelay": 100, "tid": "TID_SPELL_WALLBREAKERS", "tidInfo": "TID_SPELL_INFO_EVO_WALLBREAKERS", "source": "spells_evolved", "summonCharacterData": {"name": "Wallbreaker_EV1", "base": "Wallbreaker", "source": "characters_evo", "hitpoints": 207, "sightRange": 7000, "range": 500, "loadTime": 1000, "projectileData": {"name": "WallbreakerProjectile_EV1", "base": "WallbreakerProjectile", "damage": 245, "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE"}, "onKilledActionData": {"name": "WallBreaker_EV1_SpawnMini", "source": "actions", "nextAction.spawnData": "WallbreakerBarrelExplosion_EV1", "spawnDataData": {"name": "Wallbreaker_mini", "rarity": "Epic", "sightRange": 7000, "deployTime": 1000, "speed": 120, "hitpoints": 103, "hitSpeed": 1200, "loadTime": 1000, "range": 500, "areaDamageRadius": 1500, "collisionRadius": 400, "kamikaze": true, "tid": "TID_CHARACTER_WALLBREAKER_MINI", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "WallbreakerMiniProjectile", "rarity": "Epic", "speed": 1000, "damage": 123, "radius": 1500, "projectileRange": 1, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}}}, "summonCharacterData": {"name": "Wallbreaker", "rarity": "Epic", "sightRange": 7000, "deployTime": 1000, "speed": 120, "hitpoints": 207, "hitSpeed": 1200, "loadTime": 1000, "range": 500, "areaDamageRadius": 1500, "collisionRadius": 400, "kamikaze": true, "tid": "TID_SPELL_BOMBER", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "WallbreakerProjectile", "rarity": "Epic", "speed": 1000, "damage": 245, "radius": 1500, "projectileRange": 1, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Wall Breakers"}, {"name": "RoyalHogs", "iconFile": "royal_hog", "highresImageFilename": "image/chr/royal_hogs_dl.png", "unlockArena": "Arena7", "rarity": "Rare", "manaCost": 5, "summonNumber": 4, "summonRadius": 1, "summonDeployDelay": 100, "tid": "TID_SPELL_ROYAL_HOGS", "tidInfo": "TID_SPELL_INFO_ROYAL_HOGS", "id": 26000059, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "RoyalHog", "rarity": "Rare", "sightRange": 9500, "deployTime": 1000, "speed": 120, "hitpoints": 395, "hitSpeed": 1200, "loadTime": 800, "damage": 35, "range": 750, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_SPELL_ROYAL_HOGS", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5"}, "englishName": "Royal Hogs"}, {"name": "GoblinGiant", "iconFile": "goblin_giant", "highresImageFilename": "image/chr/goblin_giant_dl.png", "unlockArena": "Arena9", "rarity": "Epic", "manaCost": 6, "summonNumber": 1, "tid": "TID_SPELL_GOBLIN_GIANT", "tidInfo": "TID_SPELL_INFO_GOBLIN_GIANT", "id": 26000060, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"fileName": "sc/chr_goblin_giant_evolution.sc", "blueExportName": "Goblin_Giant_Evolution", "redExportName": "Goblin_Giant_Evolution_red", "healthBar": "High_2_Segments_EV1", "scale": 92, "shadowScaleX": 85, "shadowScaleY": 80, "shadowY": 20, "shadowSkew": 25, "healthBarOffsetYRed": -36, "healthBarOffsetYBlue": -15, "visualActions": ["goblin_giant_evolution_bag"], "deathEffect": "Death_card_evolution_M", "spawnEffect": "De<PERSON>ly_Go<PERSON>_Giant_Evolution", "customSpawnFilter": "filter_card_evolution", "deployBaseAnimExportName": "filter_deploy_unit_legendary", "clonedVersion": "GoblinGiant", "name": "GoblinGiant_EV1", "source": "ext", "hitpoints": 2020, "sightRange": 7500, "range": 1200, "loadTime": 700, "hitSpeed": 1500, "baseData": {"name": "GoblinGiant", "rarity": "Epic", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 2020, "hitSpeed": 1500, "loadTime": 700, "damage": 110, "range": 1200, "collisionRadius": 750, "spawnNumber": 2, "tid": "TID_SPELL_GIANT", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "spawnCharacterData": {"name": "SpearGoblinGiant", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "deathSpawnCount": 1, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "deathSpawnCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}}}, "onStartingActionData": {"healthPercentages": [50, 50, 50], "name": "GoblinGiant_EV1_trigger_at_health", "source": "actions", "actionsData": [{"interval": 1500, "name": "GoblinGiant_EV1_continuous_spawn", "source": "actions", "actionToExecuteData": {"name": "GoblinGiant_EV1_spawn_forward", "source": "actions", "spawnDataData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}}}, {"name": "GoblinGiant_EV1_start_spawn_effect", "source": "actions"}, {"name": "FirstJulioActionEver", "source": "actions"}]}, "spawnCharacterData": {"name": "SpearGoblinGiant_EV1", "source": "ext", "hitpoints": 52, "sightRange": 5500, "range": 5500, "loadTime": 1300, "hitSpeed": 1700, "baseData": {"name": "SpearGoblinGiant", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "deathSpawnCount": 1, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "deathSpawnCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}}}}, "summonCharacterData": {"name": "GoblinGiant", "rarity": "Epic", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 2020, "hitSpeed": 1500, "loadTime": 700, "damage": 110, "range": 1200, "collisionRadius": 750, "spawnNumber": 2, "tid": "TID_SPELL_GIANT", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "spawnCharacterData": {"name": "SpearGoblinGiant", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "deathSpawnCount": 1, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "deathSpawnCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}}}, "englishName": "Goblin Giant"}, {"name": "Fisherman", "iconFile": "fisherman", "highresImageFilename": "image/chr/fisherman_dl.png", "unlockArena": "Arena_L1", "rarity": "Legendary", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_FISHERMAN", "tidInfo": "TID_SPELL_INFO_FISHERMAN", "id": 26000061, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Fisherman", "rarity": "Legendary", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 720, "hitSpeed": 1300, "loadTime": 1200, "damage": 160, "range": 1200, "specialMinRange": 3500, "specialRange": 7000, "specialLoadTime": 1300, "collisionRadius": 500, "tid": "TID_SPELL_KNIGHT", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "projectileSpecialData": {"name": "FishermanProjectile", "rarity": "Legendary", "speed": 800, "buffTime": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "targetBuffData": {"name": "IceWizardSlowDown", "rarity": "Common", "tid": "TID_SPELL_ATTRIBUTE_SLOWDOWN", "hitSpeedMultiplier": -35, "speedMultiplier": -35, "spawnSpeedMultiplier": -35, "source": "character_buffs"}}}, "englishName": "Fisherman"}, {"name": "EliteArcher", "iconFile": "magic_archer", "highresImageFilename": "image/chr/magic_archer_dl.png", "unlockArena": "Arena13", "rarity": "Legendary", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_ELITE_ARCHER", "tidInfo": "TID_SPELL_INFO_ELITE_ARCHER", "id": 26000062, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "EliteArcher", "rarity": "Legendary", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 439, "hitSpeed": 1100, "loadTime": 400, "range": 7000, "collisionRadius": 600, "tid": "TID_CHARACTER_ARCHER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "EliteArcherArrow", "rarity": "Legendary", "speed": 1000, "damage": 111, "radius": 250, "projectileRange": 11000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Magic Archer"}, {"name": "ElectroDragon", "iconFile": "electro_dragon", "highresImageFilename": "image/chr/electro_dragon_dl.png", "unlockArena": "Arena_T", "rarity": "Epic", "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_ELECTRO_DRAGON", "tidInfo": "TID_SPELL_INFO_ELECTRO_DRAGON", "id": 26000063, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "ElectroDragon_EV1", "iconFile": "Electro_Dragon_evolution_card", "highresImageFilename": "image/chr_evolution/electro_dragon_evolution_dl.png", "unlockArena": "Arena_T", "rarity": "Epic", "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_ELECTRO_DRAGON", "tidInfo": "TID_SPELL_INFO_EVO_ELECTRO_DRAGON", "source": "spells_evolved", "summonCharacterData": {"useAnimator": true, "fileName": "sc/chr_electro_dragon_evolution.sc", "blueExportName": "electro_dragon_evo", "redExportName": "electro_dragon_evo_red", "scale": 90, "spawnEffect": "evo_electro_dragon_deploy", "moveEffect": "evo_electro_dragon_steps", "deathEffect": "evo_electro_dragon_die", "projectileEffect": "evo_electro_dragon_attack", "clonedVersion": "ElectroDragon", "statsTags": {"hitSpeed": "hit_speed", "hitpoints": "hitpoints", "speed": "speed", "range": "range", "targets": "targets"}, "attackSequenceList": [{"doAttackAction": "electro_dragon_ev1_attack_group"}], "name": "Electro_dragon_ev1", "source": "ext", "hitpoints": 594, "sightRange": 5500, "range": 3500, "loadTime": 1400, "hitSpeed": 2100, "baseData": {"name": "ElectroDragon", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 594, "hitSpeed": 2100, "loadTime": 1400, "range": 3500, "collisionRadius": 600, "tid": "TID_SPELL_ELECTRO_DRAGON", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "ElectroDragonProjectile", "rarity": "Epic", "speed": 2000, "damage": 120, "buffTime": 500, "chainedHitCount": 3, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "targetBuffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}}}, "summonCharacterData": {"name": "ElectroDragon", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 594, "hitSpeed": 2100, "loadTime": 1400, "range": 3500, "collisionRadius": 600, "tid": "TID_SPELL_ELECTRO_DRAGON", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "ElectroDragonProjectile", "rarity": "Epic", "speed": 2000, "damage": 120, "buffTime": 500, "chainedHitCount": 3, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "targetBuffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}, "englishName": "Electro Dragon"}, {"name": "Firecracker", "iconFile": "firecracker", "highresImageFilename": "image/chr/firecracker_dl.png", "unlockArena": "Arena_T", "rarity": "Common", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_FIRECRACKER", "tidInfo": "TID_SPELL_INFO_FIRECRACKER", "id": 26000064, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "evolvedSpellsData": {"name": "Firecracker_EV1", "iconFile": "firecracker_evolution_card", "highresImageFilename": "image/chr_evolution/firecracker_evolution_dl.png", "unlockArena": "Arena_T", "rarity": "Common", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_FIRECRACKER", "tidInfo": "TID_SPELL_INFO_EVO_FIRECRACKER", "source": "spells_evolved", "summonCharacterData": {"name": "Firecracker_EV1", "base": "Firecracker", "source": "characters_evo", "hitpoints": 119, "sightRange": 8500, "range": 6000, "loadTime": 2350, "projectileData": {"name": "FirecrackerProjectile_EV1", "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "spawnAreaEffectObjectData": {"name": "FirecrackerFireworksBig_EV1", "rarity": "Common", "lifeDuration": 3000, "radius": 2500, "hitSpeed": 250, "buffTime": 400, "source": "area_effect_objects_evo", "buffData": {"name": "FirecrackerFireworks_EV1", "rarity": "Common", "crownTowerDamagePercent": -70, "damagePerSecond": 20, "hitFrequency": 250, "speedMultiplier": -15, "source": "character_buffs_evo"}}, "spawnProjectileData": {"name": "FirecrackerExplosion_EV1", "damage": 25, "spawnCount": 5, "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnAreaEffectObjectData": {"name": "FirecrackerFireworksSmall_EV1", "rarity": "Common", "lifeDuration": 2500, "radius": 1200, "hitSpeed": 250, "buffTime": 400, "source": "area_effect_objects_evo", "buffData": {"name": "FirecrackerFireworks_EV1", "rarity": "Common", "crownTowerDamagePercent": -70, "damagePerSecond": 20, "hitFrequency": 250, "speedMultiplier": -15, "source": "character_buffs_evo"}}}}}}, "summonCharacterData": {"name": "Firecracker", "rarity": "Common", "sightRange": 8500, "deployTime": 1000, "speed": 90, "hitpoints": 119, "hitSpeed": 3000, "loadTime": 2350, "range": 6000, "collisionRadius": 500, "tid": "TID_SPELL_FIRECRACKER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_4", "projectileData": {"name": "FirecrackerProjectile", "rarity": "Common", "speed": 400, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "spawnProjectileData": {"name": "FirecrackerExplosion", "rarity": "Common", "speed": 550, "damage": 25, "radius": 400, "projectileRange": 5000, "spawnCount": 5, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}, "englishName": "Firecracker"}, {"name": "MightyMiner", "iconFile": "mightyminer", "highresImageFilename": "image/chr_champions/champion_hires_mighty_miner_dl.png", "unlockArena": "Arena_L3", "rarity": "Champion", "manaCost": 4, "tid": "TID_SPELL_MIGHTY_MINER", "tidInfo": "TID_SPELL_INFO_MIGHTY_MINER", "id": 26000065, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "MightyMiner", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 879, "hitSpeed": 400, "loadTime": 700, "damage": 16, "range": 1600, "collisionRadius": 500, "tid": "TID_SPELL_KNIGHT", "variableDamage2": 80, "variableDamage3": 160, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "abilityData": {"name": "MightyMinerLaneSwitch", "tid": "TID_ABILITY_NAME_MIGHTY_MINER_LANE_SWITCH", "tidInfo": "TID_ABILITY_DESCRIPTION_MIGHTY_MINER", "manaCost": 1, "cooldown": 13000, "activationSpawnDeployTime": 0, "source": "character_abilities", "activationSpawnCharacterData": {"name": "MightyMinerBomb", "rarity": "Common", "deployTime": 1000, "deathDamage": 130, "collisionRadius": 450, "tid": "TID_BUILDING_GIANT_SKELETON_BOMB", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}, "englishName": "Mighty Miner"}, {"name": "SuperWitch", "iconFile": "superwitch", "highresImageFilename": "image/chr/witch_dl.png", "unlockArena": "Arena5", "rarity": "Legendary", "notVisible": true, "manaCost": 6, "summonNumber": 1, "tid": "TID_SPELL_SUPER_WITCH", "tidInfo": "TID_SPELL_INFO_WITCH", "id": 26000066, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperWitch", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 880, "hitSpeed": 1100, "loadTime": 700, "range": 5500, "collisionRadius": 500, "spawnNumber": 4, "spawnPauseTime": 5000, "tid": "TID_SPELL_WITCH", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "VoodooProjectile", "rarity": "Legendary", "speed": 600, "damage": 110, "buffTime": 5000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "targetBuffData": {"name": "VoodooCurse", "rarity": "Legendary", "deathSpawnCount": 1, "source": "character_buffs", "deathSpawnData": {"name": "VoodooHog", "rarity": "Legendary", "sightRange": 9500, "deployTime": 200, "speed": 120, "hitpoints": 520, "hitSpeed": 1200, "loadTime": 950, "damage": 44, "range": 750, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_SPELL_HOG", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5"}}}, "spawnCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "spawnCharacter2Data": {"name": "Bat", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 32, "hitSpeed": 1300, "loadTime": 700, "damage": 32, "range": 1200, "collisionRadius": 500, "tid": "TID_CHARACTER_BAT", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5"}}, "englishName": "Super Witch"}, {"name": "ElixirGolem", "iconFile": "elixir_golem", "highresImageFilename": "image/chr/elixir_golem_dl.png", "unlockArena": "Arena14", "rarity": "Rare", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_ELIXIR_GOLEM", "tidInfo": "TID_SPELL_INFO_ELIXIR_GOLEM", "id": 26000067, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "ElixirGolem1", "rarity": "Rare", "sightRange": 7500, "deployTime": 1000, "speed": 45, "hitpoints": 740, "hitSpeed": 1100, "loadTime": 300, "damage": 120, "range": 800, "collisionRadius": 600, "deathSpawnCount": 2, "tid": "TID_CHARACTER_ELIXIR_GOLEM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "deathSpawnCharacterData": {"name": "ElixirGolem2", "rarity": "Rare", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 360, "hitSpeed": 1100, "loadTime": 300, "damage": 60, "range": 800, "collisionRadius": 500, "deathSpawnCount": 2, "tid": "TID_CHARACTER_MINI_GOLEM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "deathSpawnCharacterData": {"name": "ElixirGolem4", "rarity": "Rare", "sightRange": 7500, "deployTime": 1000, "speed": 90, "hitpoints": 170, "hitSpeed": 1100, "loadTime": 300, "damage": 30, "range": 800, "collisionRadius": 400, "tid": "TID_CHARACTER_MICRO_GOLEM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_4"}}}, "englishName": "<PERSON><PERSON><PERSON>"}, {"name": "BattleHealer", "iconFile": "battle_healer", "highresImageFilename": "image/chr/battle_healer_dl.png", "unlockArena": "Arena8", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "radius": 2500, "tid": "TID_SPELL_BATTLEHEALER", "tidInfo": "TID_SPELL_INFO_BATTLEHEALER", "id": 26000068, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "BattleHealer", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 810, "hitSpeed": 1500, "loadTime": 1200, "damage": 70, "range": 1600, "collisionRadius": 500, "tid": "TID_SPELL_KNIGHT", "buffWhenNotAttackingTime": 5000, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "spawnAreaObjectData": {"name": "BattleHealerSpawnHeal", "rarity": "Rare", "lifeDuration": 1000, "radius": 2500, "hitSpeed": 0, "buffTime": 1000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "BattleHealerSpawnBuff", "rarity": "Rare", "hitFrequency": 250, "healPerSecond": 95, "source": "character_buffs"}}, "areaEffectOnHitData": {"name": "BattleHealerHeal", "rarity": "Rare", "lifeDuration": 50, "radius": 4000, "hitSpeed": 0, "buffTime": 1000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "BattleHealerAll", "rarity": "Rare", "hitFrequency": 250, "healPerSecond": 48, "source": "character_buffs"}}}, "englishName": "Battle Healer"}, {"name": "SkeletonKing", "iconFile": "skeletonking", "highresImageFilename": "image/chr_champions/champion_hires_skeleton_king_dl.png", "unlockArena": "Arena_L2", "rarity": "Champion", "manaCost": 4, "tid": "TID_SPELL_SKELETON_KING", "tidInfo": "TID_SPELL_INFO_SKELETON_KING", "id": 26000069, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SkeletonKing", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 898, "hitSpeed": 1600, "loadTime": 1300, "damage": 80, "range": 1200, "areaDamageRadius": 1300, "collisionRadius": 1000, "tid": "TID_SPELL_KNIGHT", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "abilityData": {"name": "SkeletonKing", "tid": "TID_ABILITY_NAME_SKELETON_KING_RESURRECT", "tidInfo": "TID_ABILITY_DESCRIPTION_SKELETON_KING", "manaCost": 2, "cooldown": 20000, "resurrectBaseCount": 6, "spawnLimit": 16, "source": "character_abilities", "areaEffectObjectData": {"name": "SkeletonKingGraveyard", "rarity": "Common", "lifeDuration": 10000, "radius": 4000, "spawnInterval": 250, "spawnTime": 400, "spawnClones": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "spawnCharacterData": {"name": "SkeletonKingSkeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}}}, "englishName": "Skeleton King"}, {"name": "SuperLavaHound", "iconFile": "super_lava_hound", "highresImageFilename": "image/chr/lava_hound_dl.png", "unlockArena": "Arena_L", "rarity": "Legendary", "notVisible": true, "manaCost": 8, "tid": "TID_SPELL_SUPER_LAVA_HOUND", "tidInfo": "TID_SPELL_INFO_LAVA_HOUND", "id": 26000070, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperLavaHound", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 45, "hitpoints": 2800, "hitSpeed": 1300, "loadTime": 300, "range": 3500, "collisionRadius": 750, "deathSpawnCount": 2, "tid": "TID_SPELL_SUPER_LAVA_HOUND", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "projectileData": {"name": "LavaHoundProjectile", "rarity": "Legendary", "speed": 400, "damage": 45, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "deathSpawnCharacterData": {"name": "SuperLavaHound2", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 45, "hitpoints": 1500, "hitSpeed": 900, "loadTime": 300, "range": 3500, "collisionRadius": 750, "deathSpawnCount": 4, "tid": "TID_SPELL_LAVA_HOUND", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "projectileData": {"name": "LavaHoundProjectile", "rarity": "Legendary", "speed": 400, "damage": 45, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "deathSpawnCharacterData": {"name": "LavaPups", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 179, "hitSpeed": 1700, "loadTime": 700, "range": 1600, "collisionRadius": 450, "tid": "TID_CHARACTER_LAVA_PUPS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "LavaPupProjectile", "rarity": "Legendary", "speed": 500, "damage": 75, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}, "deathSpawnProjectileData": {"name": "FireWallProjectile", "rarity": "Legendary", "speed": 600, "damage": 211, "crownTowerDamagePercent": -70, "pushback": 500, "radius": 2500, "spawnChain": 2, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "spawnProjectileData": {"name": "FireWallMovingProjectile", "rarity": "Legendary", "speed": 600, "damage": 211, "crownTowerDamagePercent": -70, "pushback": 500, "radius": 2500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}, "englishName": "Super Lava Hound"}, {"name": "SuperEliteArcher", "iconFile": "supermagic<PERSON><PERSON>", "highresImageFilename": "image/chr/magic_archer_dl.png", "unlockArena": "Arena13", "rarity": "Legendary", "notVisible": true, "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_SUPER_ELITE_ARCHER", "tidInfo": "TID_SPELL_INFO_ELITE_ARCHER", "id": 26000071, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperEliteArcher", "rarity": "Legendary", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 580, "hitSpeed": 2000, "loadTime": 500, "range": 7000, "collisionRadius": 600, "tid": "TID_CHARACTER_ARCHER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "SuperEliteArcherArrow", "rarity": "Legendary", "speed": 1000, "damage": 1, "radius": 250, "buffTime": 4000, "projectileRange": 11000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "targetBuffData": {"name": "SuperEliteArcherCharm", "rarity": "Legendary", "tid": "TID_SPELL_RAGE", "switchTeam": true, "source": "character_buffs"}}}, "englishName": "Super Magic Archer"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "iconFile": "archerqueen", "highresImageFilename": "image/chr_champions/champion_hires_archer_queen_dl.png", "unlockArena": "Arena_L3", "rarity": "Champion", "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_ARCHER_QUEEN", "tidInfo": "TID_SPELL_INFO_ARCHER_QUEEN", "id": 26000072, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 391, "hitSpeed": 1200, "loadTime": 900, "range": 5000, "collisionRadius": 500, "tid": "TID_CHARACTER_ARCHER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "speed": 800, "damage": 88, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "abilityData": {"name": "<PERSON><PERSON><PERSON>enRapid", "tid": "TID_ABILITY_NAME_ARCHER_QUEEN_INVISIBILITY_RUSH", "tidInfo": "TID_ABILITY_DESCRIPTION_ARCHER_QUEEN", "manaCost": 1, "cooldown": 17000, "buffTime": 3500, "source": "character_abilities", "buffData": {"name": "<PERSON><PERSON><PERSON>enRapid", "rarity": "Common", "hitSpeedMultiplier": 280, "speedMultiplier": -25, "source": "character_buffs"}}}, "englishName": "<PERSON>"}, {"name": "SuperHogRider", "iconFile": "superhogrider", "highresImageFilename": "image/chr/hog_rider_dl.png", "unlockArena": "Arena5", "rarity": "Legendary", "notVisible": true, "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_SUPER_HOG_RIDER", "tidInfo": "TID_SPELL_INFO_HOG_RIDER", "id": 26000073, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperHogRider", "rarity": "Legendary", "sightRange": 9500, "deployTime": 1000, "speed": 120, "hitpoints": 1400, "hitSpeed": 1600, "loadTime": 1000, "damage": 265, "range": 800, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "spawnNumber": 1, "spawnPauseTime": 4000, "tid": "TID_SPELL_HOG_RIDER", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5", "spawnCharacterData": {"name": "SantaPresent", "rarity": "Epic", "deployTime": 1000, "source": "buildings", "deathAreaEffectData": {"name": "BoostInvisibilityBottle", "rarity": "Epic", "lifeDuration": 10000, "radius": 1000, "hitSpeed": 300, "buffTime": 500, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "DummyCollisionBoostEpic", "rarity": "Epic", "source": "character_buffs"}, "deathSpawnCharacterData": {"name": "InvisibilityBottle", "rarity": "Epic", "deployTime": 500, "source": "buildings", "deathAreaEffectData": {"name": "InvisibilityArea", "rarity": "Epic", "lifeDuration": 1000, "radius": 3000, "buffTime": 3000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "InvisibilityTemp", "rarity": "Epic", "source": "character_buffs"}}}}}}, "englishName": "Santa Hog Rider"}, {"name": "GoldenKnight", "iconFile": "goldenknight", "highresImageFilename": "image/chr_champions/champion_hires_golden_knight_dl.png", "unlockArena": "Arena_L2", "rarity": "Champion", "manaCost": 4, "tid": "TID_SPELL_GOLDEN_KNIGHT", "tidInfo": "TID_SPELL_INFO_GOLDEN_KNIGHT", "id": 26000074, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "GoldenKnight", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 703, "hitSpeed": 900, "loadTime": 700, "damage": 63, "range": 1200, "collisionRadius": 800, "dashDamage": 131, "dashCount": 10, "dashSecondaryRange": 5500, "jumpSpeed": 400, "tid": "TID_SPELL_KNIGHT", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "abilityData": {"name": "GoldenKnightChain", "tid": "TID_ABILITY_NAME_GOLDEN_KNIGHT_CHAIN_ATTACK", "tidInfo": "TID_ABILITY_DESCRIPTION_GOLDEN_KNIGHT", "manaCost": 1, "cooldown": 8000, "dashRange": 5500, "source": "character_abilities"}}, "englishName": "Golden Knight"}, {"name": "SuperIceGolemite", "iconFile": "supericegolem", "highresImageFilename": "image/chr/ice_golem_dl.png", "unlockArena": "Arena8", "rarity": "Legendary", "notVisible": true, "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_SUPERICEGOLEMITE", "tidInfo": "TID_SPELL_INFO_ICEGOLEMITE", "id": 26000075, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperIceGolemite", "rarity": "Legendary", "sightRange": 7000, "deployTime": 1000, "speed": 30, "hitpoints": 3000, "hitSpeed": 2500, "loadTime": 1500, "damage": 100, "range": 750, "deathDamage": 40, "collisionRadius": 700, "tid": "TID_CHARACTER_SMALL_GOLEM", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "deathAreaEffectData": {"name": "SuperFreezeIceGolemite", "rarity": "Legendary", "lifeDuration": 4000, "radius": 40000, "damage": 72, "crownTowerDamagePercent": -70, "buffTime": 4000, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "Freeze", "rarity": "Common", "tid": "TID_SPELL_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}, "englishName": "Super Ice Golem"}, {"name": "<PERSON>", "iconFile": "monk", "highresImageFilename": "image/chr_champions/champion_hires_monk_dl.png", "unlockArena": "Arena_L4", "rarity": "Champion", "manaCost": 5, "tid": "TID_SPELL_MONK", "tidInfo": "TID_SPELL_INFO_MONK", "id": 26000077, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 840, "hitSpeed": 800, "loadTime": 600, "damage": 55, "range": 1200, "collisionRadius": 500, "tid": "TID_SPELL_MONK", "variableDamage2": 55, "variableDamage3": 165, "isMeleePushbackAll3": true, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "abilityData": {"name": "Deflect", "tid": "TID_ABILITY_NAME_MONK_DEFLECT", "tidInfo": "TID_ABILITY_DESCRIPTION_MONK", "manaCost": 1, "cooldown": 17000, "buffTime": 4000, "source": "character_abilities", "buffData": {"name": "ShieldBoostMonk", "rarity": "Common", "damageReduction": 80, "source": "character_buffs"}, "areaEffectObjectData": {"name": "Deflect", "rarity": "Common", "lifeDuration": 4000, "radius": 1500, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects"}, "onActivationActionData": {"name": "monk_deflect_tags", "source": "actions"}}}, "englishName": "<PERSON>"}, {"name": "SuperArcher", "iconFile": "superarcher", "highresImageFilename": "image/chr/archers.png", "unlockArena": "Arena_L10", "rarity": "Legendary", "notVisible": true, "manaCost": 3, "summonNumber": 2, "summonDeployDelay": 100, "tid": "TID_SPELL_SUPER_ARCHERS", "tidInfo": "TID_SPELL_INFO_ARCHERS", "id": 26000078, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperArcher", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 580, "hitSpeed": 2000, "loadTime": 1500, "range": 5000, "collisionRadius": 500, "tid": "TID_CHARACTER_ARCHER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "SuperArcherChargeArrow", "rarity": "Legendary", "speed": 400, "radius": 250, "projectileRange": 10000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnAreaEffectObjectData": {"name": "SuperArcherChargePull", "rarity": "Legendary", "lifeDuration": 1050, "radius": 3000, "hitSpeed": 50, "buffTime": 500, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "SuperArcherTornado", "rarity": "Legendary", "crownTowerDamagePercent": -50, "damagePerSecond": 200, "hitFrequency": 500, "source": "character_buffs"}}}}, "englishName": "Super Archers"}, {"name": "RoyalRecruits_Chess", "iconFile": "royal_recruits", "highresImageFilename": "image/chr/royal_recruits_dl.png", "unlockArena": "Arena7", "rarity": "Common", "notVisible": true, "manaCost": 7, "summonNumber": 8, "summonRadius": 100, "summonDeployDelay": 100, "tid": "TID_SPELL_GUARD_BATTALION", "tidInfo": "TID_SPELL_INFO_GUARD_BATTALION", "id": 26000079, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Recruit_Chess", "rarity": "Common", "sightRange": 1600, "deployTime": 1000, "hitpoints": 208, "hitSpeed": 1300, "loadTime": 800, "damage": 52, "range": 1600, "collisionRadius": 500, "tid": "TID_SPELL_GUARD_BATTALION", "shieldHitpoints": 94, "source": "characters", "tidTarget": "TID_TARGETS_GROUND"}, "englishName": "Royal Recruits"}, {"name": "SkeletonDragons", "iconFile": "skeletondragon", "highresImageFilename": "image/chr/skeleton_dragon_dl.png", "unlockArena": "Arena4", "rarity": "Common", "manaCost": 4, "summonNumber": 2, "summonRadius": 1500, "summonDeployDelay": 100, "tid": "TID_SPELL_SKELETONDRAGONS", "tidInfo": "TID_SPELL_INFO_SKELETONDRAGONS", "id": 26000080, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SkeletonDragon", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 219, "hitSpeed": 1900, "loadTime": 1500, "range": 3500, "collisionRadius": 900, "tid": "TID_SPELL_SKELETONDRAGONS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_4", "projectileData": {"name": "SkeletonDragonProjectile", "rarity": "Common", "speed": 500, "damage": 63, "radius": 800, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "englishName": "Skeleton Dragons"}, {"name": "SuperHogRiderTerry", "iconFile": "hogrider_champion", "highresImageFilename": "image/chr/hog_rider_dl.png", "unlockArena": "Arena_L2", "rarity": "Champion", "notVisible": true, "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_SUPER_HOG_RIDER_TERRY", "tidInfo": "TID_SPELL_INFO_SUPER_HOG_RIDER_TERRY", "id": 26000081, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperHogRider_Terry", "rarity": "Champion", "sightRange": 9500, "deployTime": 1000, "speed": 120, "hitpoints": 2300, "hitSpeed": 1600, "loadTime": 1000, "damage": 350, "range": 800, "collisionRadius": 600, "jumpHeight": 30000, "dashDamage": 500, "jumpSpeed": 150, "tid": "TID_SPELL_HOG_RIDER", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5", "abilityData": {"name": "SuperHogJump", "tid": "TID_ABILITY_NAME_SUPER_HOG_RIDER_TERRY_JUMP", "tidInfo": "TID_ABILITY_DESCRIPTION_SUPER_HOG_RIDER_TERRY", "manaCost": 1, "cooldown": 7000, "dashRange": 99999, "source": "character_abilities"}}, "englishName": "<PERSON>"}, {"name": "SuperMiniPekka", "iconFile": "superminipekka", "highresImageFilename": "image/chr/mini_pekka.png", "unlockArena": "Arena14", "rarity": "Legendary", "notVisible": true, "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_SUPERMINIPEKKA", "tidInfo": "TID_SPELL_INFO_MINIPEKKA", "id": 26000082, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "SuperMiniPekka", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 1300, "hitSpeed": 1600, "loadTime": 1100, "range": 800, "collisionRadius": 450, "spawnNumber": 1, "spawnPauseTime": 3000, "tid": "TID_SPELL_MINIPEKKA", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4", "projectileData": {"name": "SuperMiniPekkaHit", "rarity": "Legendary", "speed": 3000, "damage": 99999, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "spawnCharacterData": {"name": "SuperMiniPekkaPancakes", "rarity": "Rare", "deployTime": 1000, "collisionRadius": 450, "tid": "TID_BUILDING_GIANT_SKELETON_BOMB", "source": "buildings", "deathAreaEffectData": {"name": "SuperMiniPekkaPancakesHeal", "rarity": "Rare", "lifeDuration": 1000, "radius": 2500, "hitSpeed": 0, "buffTime": 1000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "SuperMiniPekkaPancakesHeal", "rarity": "Rare", "hitFrequency": 250, "healPerSecond": 100, "source": "character_buffs"}}}}, "englishName": "Super Mini P.E.K.K.A"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "iconFile": "motherwitch", "highresImageFilename": "image/chr/mother_witch_dl.png", "unlockArena": "Arena_L1", "rarity": "Legendary", "manaCost": 4, "tid": "TID_SPELL_WITCHMOTHER", "tidInfo": "TID_SPELL_INFO_WITCHMOTHER", "id": 26000083, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 439, "hitSpeed": 1000, "loadTime": 700, "range": 5500, "buffOnDamageTime": 5000, "collisionRadius": 500, "tid": "TID_SPELL_WITCHMOTHER", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "VoodooProjectile", "rarity": "Legendary", "speed": 600, "damage": 110, "buffTime": 5000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "targetBuffData": {"name": "VoodooCurse", "rarity": "Legendary", "deathSpawnCount": 1, "source": "character_buffs", "deathSpawnData": {"name": "VoodooHog", "rarity": "Legendary", "sightRange": 9500, "deployTime": 200, "speed": 120, "hitpoints": 520, "hitSpeed": 1200, "loadTime": 950, "damage": 44, "range": 750, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_SPELL_HOG", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5"}}}, "buffOnDamageData": {"name": "VoodooCurse", "rarity": "Legendary", "deathSpawnCount": 1, "source": "character_buffs", "deathSpawnData": {"name": "VoodooHog", "rarity": "Legendary", "sightRange": 9500, "deployTime": 200, "speed": 120, "hitpoints": 520, "hitSpeed": 1200, "loadTime": 950, "damage": 44, "range": 750, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_SPELL_HOG", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5"}}}, "englishName": "Mother Witch"}, {"name": "ElectroSpirit", "iconFile": "electrospirit", "highresImageFilename": "image/chr/electro_spirit_dl.png", "unlockArena": "Arena4", "rarity": "Common", "manaCost": 1, "summonNumber": 1, "tid": "TID_SPELL_ELECTRO_SPIRIT", "tidInfo": "TID_SPELL_INFO_ELECTRO_SPIRIT", "id": 26000084, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "ElectroSpirit", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 90, "hitSpeed": 300, "loadTime": 100, "range": 2500, "collisionRadius": 400, "kamikaze": true, "tid": "TID_SPELL_ELECTRO_SPIRIT", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "ElectroSpiritProjectile", "rarity": "Common", "speed": 2000, "damage": 39, "buffTime": 500, "chainedHitCount": 9, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "targetBuffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}, "englishName": "Electro Spirit"}, {"name": "ElectroGiant", "iconFile": "electrogiant", "highresImageFilename": "image/chr/electro_giant_dl.png", "unlockArena": "Arena13", "rarity": "Epic", "manaCost": 7, "summonNumber": 1, "tid": "TID_SPELL_ELECTRO_GIANT", "tidInfo": "TID_SPELL_INFO_ELECTRO_GIANT", "id": 26000085, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "ElectroGiant", "rarity": "Epic", "sightRange": 7500, "deployTime": 1000, "speed": 45, "hitpoints": 2410, "hitSpeed": 2100, "loadTime": 600, "damage": 102, "range": 1200, "collisionRadius": 750, "tid": "TID_SPELL_BABY_DRAGON", "reflectedAttackBuffDuration": 500, "reflectedAttackRadius": 2000, "reflectedAttackDamage": 120, "reflectAttackCrownTowerDamage": 80, "reflectedAttackTargetedEffectSources": 2, "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_2", "reflectedAttackBuffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}, "englishName": "Electro Giant"}, {"name": "<PERSON><PERSON><PERSON>", "iconFile": "prince", "highresImageFilename": "image/chr/prince_dl.png", "unlockArena": "Arena7", "rarity": "Epic", "notVisible": true, "tribe": "Magic", "manaCost": 5, "tid": "TID_SPELL_PRINCE_BUFF", "tidInfo": "TID_SPELL_INFO_PRINCE", "id": 26000086, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "chargeRange": 300, "speed": 60, "hitpoints": 1440, "hitSpeed": 1400, "loadTime": 900, "damage": 245, "damageSpecial": 490, "range": 1600, "collisionRadius": 600, "jumpHeight": 4000, "jumpSpeed": 160, "tid": "TID_SPELL_PRINCE", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "englishName": "Raging Prince"}, {"name": "Phoenix", "iconFile": "phoenix", "highresImageFilename": "image/chr/phoenix_dl.png", "unlockArena": "Arena_T", "rarity": "Legendary", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_PHOENIX", "tidInfo": "TID_SPELL_INFO_PHOENIX", "id": 26000087, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Phoenix", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 870, "hitSpeed": 1000, "loadTime": 500, "damage": 180, "range": 1600, "collisionRadius": 500, "tid": "TID_SPELL_PHOENIX", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "deathSpawnProjectileData": {"name": "PhoenixFireball", "rarity": "Legendary", "speed": 600, "damage": 135, "crownTowerDamagePercent": -70, "pushback": 2000, "radius": 2500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "spawnCharacterData": {"name": "PhoenixEgg", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "hitpoints": 198, "hitSpeed": 1000, "loadTime": 500, "collisionRadius": 600, "spawnNumber": 1, "spawnPauseTime": 4300, "tid": "TID_SPELL_PHOENIX_EGG", "source": "characters", "spawnCharacterData": {"name": "PhoenixNoRespawn", "rarity": "Legendary", "sightRange": 5500, "deployTime": 733, "speed": 60, "hitpoints": 696, "hitSpeed": 1000, "loadTime": 500, "collisionRadius": 500, "tid": "TID_SPELL_PHOENIX", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3"}}}}, "englishName": "Phoenix"}, {"name": "Little<PERSON><PERSON><PERSON>", "iconFile": "Little<PERSON><PERSON><PERSON>", "highresImageFilename": "image/chr_champions/champion_hires_little_prince_dl.png", "unlockArena": "Arena_L4", "rarity": "Champion", "manaCost": 3, "summonNumber": 1, "tid": "TID_SPELL_LITTLE_PRINCE", "tidInfo": "TID_SPELL_INFO_LITTLE_PRINCE", "id": 26000093, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"name": "Little<PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 273, "hitSpeed": 1200, "loadTime": 800, "range": 5500, "collisionRadius": 500, "tid": "TID_SPELL_LITTLE_PRINCE", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "LittlePrinceProjectile", "rarity": "Common", "speed": 800, "damage": 39, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "abilityData": {"name": "ChampGuardianAbility", "tid": "TID_ABILITY_NAME_LITTLE_PRINCE_RESCUE", "tidInfo": "TID_ABILITY_DESCRIPTION_LITTLE_PRINCE", "manaCost": 3, "cooldown": 30000, "source": "character_abilities", "onActivationActionData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "actions", "nextAction.spawnDataData": {"name": "DummySpawnLittlePrinceGuard", "rarity": "Common", "lifeDuration": 1000, "hitSpeed": 0, "source": "area_effect_objects", "onStartingActionData": {"name": "Spawn_ChampionGuardCharge", "pushBackStrength": 2000, "pushBackDamage": 81, "source": "actions", "spawnDataData": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 300, "speed": 60, "hitpoints": 625, "hitSpeed": 1200, "loadTime": 700, "damage": 78, "range": 1200, "collisionRadius": 800, "dashMaxRange": 999999, "jumpSpeed": 400, "tid": "TID_SPELL_CHAMP_GUARD", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}}}}}, "englishName": "Little Prince"}, {"name": "TriWizards", "iconFile": "triwizards", "unlockArena": "Arena_T", "rarity": "Legendary", "notVisible": true, "tribe": "Magic", "manaCost": 7, "radius": 100, "spellAsDeploy": true, "tid": "TID_SPELL_TRIWIZARD", "tidInfo": "TID_SPELL_INFO_TRIWIZARD", "id": 26000094, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "areaEffectObjectData": {"name": "TriWizardSpawn", "rarity": "Legendary", "lifeDuration": 500, "radius": 1000, "spawnInterval": 300, "spawnTime": 100, "source": "area_effect_objects", "spawnCharacterData": {"name": "TriWizard", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 340, "hitSpeed": 1400, "loadTime": 1000, "range": 5500, "collisionRadius": 500, "tid": "TID_SPELL_WIZARD", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"name": "chr_wizardProjectile", "rarity": "Rare", "speed": 600, "damage": 133, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "onStartingActionData": {"name": "TriWizardSpawn", "source": "actions", "subActionsData": [{"name": "ElectroWizardAOE", "source": "actions", "spawnDataData": {"name": "ElectroWizardZap", "rarity": "Legendary", "lifeDuration": 1, "radius": 3000, "damage": 159, "crownTowerDamagePercent": -100, "buffTime": 500, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}, {"name": "IceWizardAOE", "source": "actions", "spawnDataData": {"name": "IceWizardCold", "rarity": "Legendary", "lifeDuration": 1, "radius": 3000, "damage": 69, "crownTowerDamagePercent": -100, "buffTime": 1000, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "IceWizardCold", "rarity": "Common", "tid": "TID_SPELL_ICEWIZARD_SLOW", "hitSpeedMultiplier": -35, "speedMultiplier": -35, "spawnSpeedMultiplier": -35, "source": "character_buffs"}}}]}}, "englishName": "Wizard Trio"}, {"name": "GoblinDemolisher", "iconFile": "goblindemolisher", "highresImageFilename": "image/chr_goblin_faction/goblin_demolisher_dl.png", "unlockArena": "Arena_T", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_GOBLIN_DEMOLISHER", "tidInfo": "TID_SPELL_INFO_GOBLIN_DEMOLISHER", "id": 26000095, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"rarity": "Common", "tid": "TID_GOBLIN_DEMOLISHER", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 508, "hitSpeed": 1200, "loadTime": 700, "range": 5000, "collisionRadius": 600, "name": "GoblinDemolisher", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"rarity": "Common", "speed": 400, "damage": 73, "radius": 1500, "name": "GoblinDemolisherProjectile", "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}, "deathSpawnProjectileData": {"rarity": "Common", "speed": 400, "damage": 158, "pushback": 2000, "radius": 2500, "name": "GoblinDemolisherDeathProjectile", "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "onStartingActionData": {"healthPercentages": [50], "name": "GoblinDemolisher_trigger_at_health", "source": "actions", "actionsData": [{"name": "GoblinDemolisher_kamikaze_transformation", "source": "actions", "newCharacterDataData": {"rarity": "Common", "speed": 120, "hitSpeed": 1200, "loadTime": 1000, "kamikaze": true, "hitpoints": 508, "sightRange": 5500, "deployTime": 1000, "tid": "TID_GOBLIN_DEMOLISHER", "collisionRadius": 600, "name": "GoblinDemolisher_kamikaze_form", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_5", "deathSpawnProjectileData": {"rarity": "Common", "speed": 400, "damage": 158, "pushback": 2000, "radius": 2500, "name": "GoblinDemolisherDeathProjectile", "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}]}}, "englishName": "<PERSON><PERSON> Demolisher"}, {"name": "GoblinMachine", "iconFile": "goblinmachine", "highresImageFilename": "image/chr_goblin_faction/goblin_machine_dl.png", "unlockArena": "Arena_L1", "rarity": "Legendary", "manaCost": 5, "summonNumber": 1, "tid": "TID_SPELL_GOBLIN_MACHINE", "tidInfo": "TID_SPELL_INFO_GOBLIN_MACHINE", "id": 26000096, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 900, "hitSpeed": 1200, "loadTime": 700, "damage": 83, "range": 1200, "collisionRadius": 750, "tid": "TID_GOBLIN_MACHINE", "name": "GoblinMachine", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "onStartingActionData": {"projectileOffsetToCharacterLookDirection": -1200, "loadTime": 1500, "targetIndicatorDelay": 0, "attackDelay": 1000, "attackCooldown": 2500, "range": 5000, "minimumRange": 2500, "targetFilter": "DefaultCharacterTargets", "gameTagsToSetToStopTargetIndication": "UNIT_CUSTOM_TAG_1", "projectileStartZ": 5000, "name": "Goblin_machine_rocket", "source": "actions", "targetAoEData": {"rarity": "Common", "lifeDuration": 9999, "name": "Goblin_machine_rocket_target_signal", "source": "area_effect_objects", "onStartingActionData": {"name": "Goblin_machine_signal_set", "source": "actions", "subActionsData": [{"name": "Goblin_machine_signal_intro", "source": "actions"}, {"name": "Goblin_machine_signal_core", "source": "actions"}]}}, "projectileData": {"name": "GoblinMachineRocketProjectile", "rarity": "Common", "speed": 250, "damage": 153, "crownTowerDamagePercent": -50, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "targetStartIndicationActionData": {"name": "Goblin_machine_rocket_load", "source": "actions"}, "onProjectileShootActionData": {"name": "Goblin_machine_rocket_hide", "source": "actions"}}}, "englishName": "Goblin Machine"}, {"name": "SuspiciousBush", "iconFile": "suspicious_bush", "highresImageFilename": "image/chr_goblin_faction/goblin_bush_dl.png", "unlockArena": "Arena13", "rarity": "Rare", "manaCost": 2, "summonNumber": 1, "tid": "TID_SPELL_SUSPICIOUS_BUSH", "tidInfo": "TID_SPELL_INFO_SUSPICIOUS_BUSH", "id": 26000097, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"rarity": "Common", "tid": "TID_SUSPICIOUS_BUSH", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 32, "hitSpeed": 300, "loadTime": 300, "damage": 0, "collisionRadius": 500, "kamikaze": true, "name": "SuspiciousBush", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "deathAreaEffectData": {"rarity": "Common", "lifeDuration": 1000, "hitSpeed": -1, "damage": 100, "name": "SuspiciousBush_DummyAEO", "source": "area_effect_objects", "onStartingActionData": {"name": "SuspiciousBush_SpawnBushGoblin", "source": "actions", "subActionsData": [{"name": "SuspiciousBush_SpawnBushGoblin1", "source": "actions", "spawnDataData": {"rarity": "Common", "tid": "TID_CHARACTER_BUSH_GOBLIN", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 119, "hitSpeed": 1400, "loadTime": 1100, "damage": 89, "range": 800, "collisionRadius": 500, "name": "<PERSON><PERSON><PERSON><PERSON>", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}, {"name": "SuspiciousBush_SpawnBushGoblin2", "source": "actions", "spawnDataData": {"rarity": "Common", "tid": "TID_CHARACTER_BUSH_GOBLIN", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 119, "hitSpeed": 1400, "loadTime": 1100, "damage": 89, "range": 800, "collisionRadius": 500, "name": "<PERSON><PERSON><PERSON><PERSON>", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}]}}, "buffWhenNotAttackingData": {"rarity": "Common", "name": "BushInvisibility", "source": "character_buffs"}}, "englishName": "Suspicious Bush"}, {"name": "SuperKnight", "iconFile": "superknight", "highresImageFilename": "image/chr/knight.png", "unlockArena": "Arena_T", "rarity": "Legendary", "notVisible": true, "tribe": "Kingdom", "manaCost": 4, "summonNumber": 1, "radius": 8000, "tid": "TID_SPELL_SUPERKNIGHT", "tidInfo": "TID_SPELL_INFO_KNIGHT", "id": 26000098, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 793, "hitSpeed": 1200, "loadTime": 700, "damage": 90, "range": 1200, "collisionRadius": 500, "tid": "TID_SPELL_KNIGHT", "buffWhenNotAttackingTime": 50, "name": "SuperKnight", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3", "buffWhenNotAttackingData": {"rarity": "Common", "name": "SuperKnight_ShieldBuff", "source": "character_buffs"}}, "englishName": "Super Knight"}, {"name": "<PERSON><PERSON>stein", "iconFile": "goblinstein_card", "highresImageFilename": "image/chr_champions/champion_hires_goblinstein.png", "unlockArena": "Arena_L4", "rarity": "Champion", "manaCost": 5, "summonNumber": 1, "summonCharacterSecondCount": 1, "summonRadius": 3500, "tid": "TID_SPELL_GOBLINSTEIN", "tidInfo": "TID_SPELL_INFO_GOBLINSTEIN", "id": 26000099, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"rarity": "Common", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 1015, "hitSpeed": 1500, "loadTime": 700, "damage": 50, "range": 1200, "collisionRadius": 750, "tid": "TID_GOBLINSTEIN", "name": "<PERSON><PERSON>stein", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3"}, "summonCharacterSecondData": {"rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 282, "hitSpeed": 1800, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "name": "<PERSON><PERSON><PERSON>_doctor", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_3", "projectileData": {"rarity": "Common", "speed": 500, "damage": 36, "buffTime": 500, "name": "Prj_go<PERSON><PERSON>_doctor", "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "targetBuffData": {"rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "name": "B<PERSON>_goblin<PERSON>_doctor", "source": "character_buffs"}}, "abilityData": {"tid": "TID_GOBLINSTEIN_ABILITY", "tidInfo": "TID_GOBLINSTEIN_ABILITY_INFO", "manaCost": 2, "cooldown": 17000, "name": "Goblinstein_ability", "source": "character_abilities", "onActivationActionData": {"spawnTime": 1250, "name": "<PERSON><PERSON><PERSON>_enable_doctor_aura", "source": "actions", "spawnDataData": {"rarity": "Common", "name": "<PERSON><PERSON><PERSON>_doctor_aura", "source": "character_buffs"}}}, "onStartingActionData": {"tetherDuration": 4000, "tetherWidth": 2000, "tetherDamage": 42, "tetherCrownTowerDamage": 9, "tetherHitInterval": 500, "tetherHitActionInterval": 1000, "name": "Goblin<PERSON>_ability_action", "source": "actions"}}, "englishName": "<PERSON><PERSON>stein"}, {"name": "SkeletonWarriors_SpookyChess", "iconFile": "skeleton_warriors", "highresImageFilename": "image/chr/guards_dl.png", "unlockArena": "Arena7", "rarity": "Common", "notVisible": true, "manaCost": 7, "summonCharacter": "SkeletonWarrior_SpookyChess", "summonNumber": 8, "summonRadius": 100, "summonDeployDelay": 100, "tid": "TID_SPELL_GUARD_BATTALION", "tidInfo": "TID_SPELL_INFO_GUARD_BATTALION", "id": 26000100, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "englishName": "Royal Recruits"}, {"name": "Giant<PERSON>uffer", "iconFile": "buff_giant_card", "highresImageFilename": "image/chr/buff_giant_dl.png", "unlockArena": "Arena9", "rarity": "Epic", "manaCost": 4, "tid": "TID_SPELL_GIANT_BUFFER", "tidInfo": "TID_SPELL_INFO_GIANT_BUFFER", "id": 26000101, "tidType": "TID_CARD_TYPE_CHARACTER", "source": "spells_characters", "summonCharacterData": {"rarity": "Common", "sightRange": 7500, "deployTime": 1000, "speed": 60, "hitpoints": 1095, "hitSpeed": 1500, "loadTime": 1000, "damage": 47, "range": 1200, "collisionRadius": 750, "tid": "TID_SPELL_GIANT_BUFFER", "name": "Giant<PERSON>uffer", "source": "characters", "tidTarget": "TID_TARGETS_BUILDINGS", "tidSpeed": "TID_SPEED_3", "abilityData": {"tid": "TID_GOBLINSTEIN_ABILITY", "tidInfo": "TID_GOBLINSTEIN_ABILITY_INFO", "manaCost": 0, "cooldown": 0, "onActivationAction": {"classType": "ActionPlayEffect"}, "name": "Giantbuffer_ability", "source": "character_abilities"}, "onStartingActionData": {"maxFriendlyTroops": 2, "targetFilter": "friendly_troops_for_chef_giant_buffer", "name": "Giantbuffer_collect_friend_troops", "source": "actions", "projectileData": {"rarity": "Common", "speed": 600, "damage": 0, "radius": 1, "name": "GiantBuffProjectile", "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE"}, "actionWhenUnitBuffedData": {"addedCrownTowerDamage": 86, "addedDamage": 86, "name": "<PERSON>bu<PERSON>_enchanting_buff", "source": "actions", "visualActionForEnemyTargetData": {"addedDamageMergeDuration": 250, "name": "Giantbuffer_target_adddamage_action", "source": "actions"}}}}, "englishName": "Rune Giant"}, {"name": "<PERSON>", "iconFile": "chaos_cannon", "highresImageFilename": "image/chr/cannon_dl.png", "unlockArena": "Arena3", "rarity": "Common", "manaCost": 3, "tid": "TID_SPELL_CANNON", "tidInfo": "TID_SPELL_INFO_CANNON", "id": 27000000, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "evolvedSpellsData": {"fileName": "sc/building_cannon_evolution.sc", "blueExportName": "building_cannon_evolution", "redExportName": "building_cannon_evolution_red", "useAnimator": true, "deployBaseAnimExportName": "filter_deploy_unit_legendary", "spawnEffect": "evo_cannon_deploy", "deathEffect": "evo_cannon_die", "blueShadowExportName": "building_cannon_evolution_red_shadow", "redShadowExportName": "building_cannon_evolution_red_shadow", "statsTags": {"hitpoints": "hitpoints", "damage": "ranged_damage", "hitSpeed": "hit_speed", "targets": "targets", "range": "range", "lifeTime": "lifetime"}, "name": "Cannon_EV1", "source": "ext", "hitpoints": 322, "sightRange": 5500, "range": 5500, "hitSpeed": 900, "baseData": {"name": "<PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "hitpoints": 322, "hitSpeed": 900, "range": 5500, "lifeTime": 30000, "collisionRadius": 600, "tid": "TID_BUILDING_CANNON", "source": "buildings", "tidTarget": "TID_TARGETS_GROUND", "projectileData": {"name": "TowerCannonball", "rarity": "Common", "speed": 1000, "damage": 83, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "onStartingActionData": {"name": "Cannon_EV1_start_action_group", "source": "actions", "subActionsData": [{"name": "Cannon_EV1_barrage", "source": "actions"}]}}, "summonCharacterData": {"name": "<PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "hitpoints": 322, "hitSpeed": 900, "range": 5500, "lifeTime": 30000, "collisionRadius": 600, "tid": "TID_BUILDING_CANNON", "source": "buildings", "tidTarget": "TID_TARGETS_GROUND", "projectileData": {"name": "TowerCannonball", "rarity": "Common", "speed": 1000, "damage": 83, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "<PERSON>"}, {"name": "GoblinHut", "iconFile": "fire_furnace", "highresImageFilename": "image/chr/goblinhut_dl.png", "unlockArena": "Arena1", "rarity": "Rare", "manaCost": 5, "tid": "TID_SPELL_GOBLIN_HUT", "tidInfo": "TID_SPELL_INFO_GOBLIN_HUT", "id": 27000001, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "GoblinHut", "rarity": "Rare", "deployTime": 1000, "hitpoints": 400, "hitSpeed": 10000, "lifeTime": 29000, "collisionRadius": 1000, "spawnInterval": 500, "spawnNumber": 3, "spawnPauseTime": 10000, "deathSpawnCount": 1, "tid": "TID_BUILDING_GOBLIN_HUT", "source": "buildings", "spawnCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "deathSpawnCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 52, "hitSpeed": 1700, "loadTime": 1300, "range": 5500, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinProjectile", "rarity": "Common", "speed": 500, "damage": 32, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}}, "englishName": "<PERSON><PERSON>"}, {"name": "Mortar", "iconFile": "building_mortar", "highresImageFilename": "image/chr/mortar_dl.png", "unlockArena": "Arena5", "rarity": "Common", "manaCost": 4, "tid": "TID_SPELL_MORTAR", "tidInfo": "TID_SPELL_INFO_MORTAR", "id": 27000002, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "evolvedSpellsData": {"name": "Mortar_EV1", "iconFile": "mortar_evolution_card", "highresImageFilename": "image/chr_evolution/mortar_evolution_dl.png", "unlockArena": "Arena6", "rarity": "Common", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_MORTAR", "tidInfo": "TID_SPELL_INFO_EVO_MORTAR", "source": "spells_evolved", "summonCharacterData": {"name": "Mortar_EV1", "base": "Mortar", "hitSpeed": 4000, "loadTime": 3000, "source": "buildings_evo", "hitpoints": 535, "sightRange": 11500, "range": 11500, "projectileData": {"name": "MortarProjectile_EV1", "damage": 104, "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}}}}, "summonCharacterData": {"name": "Mortar", "rarity": "Common", "sightRange": 11500, "deployTime": 3500, "hitpoints": 535, "hitSpeed": 5000, "loadTime": 4000, "range": 11500, "lifeTime": 30000, "collisionRadius": 600, "tid": "TID_BUILDING_MORTAR", "source": "buildings", "tidTarget": "TID_TARGETS_GROUND", "projectileData": {"name": "MortarProjectile", "rarity": "Common", "speed": 300, "damage": 104, "radius": 2000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}}, "englishName": "Mortar"}, {"name": "InfernoTower", "iconFile": "building_inferno", "unlockArena": "Arena4", "rarity": "Rare", "manaCost": 5, "tid": "TID_SPELL_INFERNO", "tidInfo": "TID_SPELL_INFO_INFERNO", "id": 27000003, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "InfernoTower", "rarity": "Rare", "sightRange": 6000, "deployTime": 1000, "hitpoints": 825, "hitSpeed": 400, "loadTime": 1200, "damage": 20, "range": 6000, "lifeTime": 30000, "collisionRadius": 600, "tid": "TID_BUILDING_INFERNO", "variableDamage2": 75, "variableDamage3": 400, "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "englishName": "Inferno Tower"}, {"name": "BombTower", "iconFile": "bomb_tower", "highresImageFilename": "image/chr/bombtower_dl.png", "unlockArena": "Arena4", "rarity": "Rare", "manaCost": 4, "tid": "TID_SPELL_BOMB_TOWER", "tidInfo": "TID_SPELL_INFO_BOMB_TOWER", "id": 27000004, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "BombTower", "rarity": "Rare", "sightRange": 6000, "deployTime": 1000, "hitpoints": 640, "hitSpeed": 1800, "loadTime": 1300, "range": 6000, "lifeTime": 30000, "collisionRadius": 600, "deathSpawnCount": 1, "tid": "TID_BUILDING_BOMB_TOWER", "source": "buildings", "tidTarget": "TID_TARGETS_GROUND", "deathSpawnCharacterData": {"name": "BombTowerBomb", "rarity": "Rare", "deployTime": 3000, "deathDamage": 105, "collisionRadius": 450, "tid": "TID_BUILDING_GIANT_SKELETON_BOMB", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "projectileData": {"name": "BombTowerProjectile", "rarity": "Rare", "speed": 500, "damage": 105, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}}, "englishName": "Bomb Tower"}, {"name": "BarbarianHut", "iconFile": "barbarian_hut", "unlockArena": "Arena9", "rarity": "Rare", "manaCost": 6, "tid": "TID_SPELL_BARBARIAN_HUT", "tidInfo": "TID_SPELL_INFO_BARBARIAN_HUT", "id": 27000005, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "BarbarianHut", "rarity": "Rare", "deployTime": 1000, "hitpoints": 550, "hitSpeed": 10000, "lifeTime": 30000, "collisionRadius": 1000, "spawnInterval": 500, "spawnNumber": 3, "spawnPauseTime": 14000, "deathSpawnCount": 1, "tid": "TID_BUILDING_BARBARIAN_HUT", "source": "buildings", "spawnCharacterData": {"name": "Barbarian", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 262, "hitSpeed": 1300, "loadTime": 900, "damage": 75, "range": 700, "collisionRadius": 500, "tid": "TID_CHARACTER_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}, "deathSpawnCharacterData": {"name": "Barbarian", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 262, "hitSpeed": 1300, "loadTime": 900, "damage": 75, "range": 700, "collisionRadius": 500, "tid": "TID_CHARACTER_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}, "englishName": "Barbarian Hut"}, {"name": "Tesla", "iconFile": "building_tesla", "highresImageFilename": "image/chr/tesla_dl.png", "unlockArena": "Arena_L", "rarity": "Common", "manaCost": 4, "tid": "TID_SPELL_TESLA", "tidInfo": "TID_SPELL_INFO_TESLA", "id": 27000006, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "evolvedSpellsData": {"name": "Tesla_EV1", "iconFile": "tesla_evolution_card", "highresImageFilename": "image/chr_evolution/tesla_evolution_dl.png", "unlockArena": "Arena_L", "rarity": "Common", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_TESLA", "tidInfo": "TID_SPELL_INFO_EVO_TESLA", "source": "spells_evolved", "summonCharacterData": {"name": "Tesla_EV1", "base": "Tesla", "source": "buildings_evo", "hitpoints": 450, "sightRange": 5500, "range": 5500, "loadTime": 700, "hitSpeed": 1100, "onStartingActionData": {"name": "Tesla_EV1_AppearStun", "source": "actions", "tidDamage": "TID_TESLAPULSE_DAMAGE", "tidDuration": "TID_TESLABUFF_PULSE", "spawnDataData": {"name": "Tesla_EV1_AppearStun", "rarity": "Common", "radius": 1, "maxRadius": 6000, "lifeDuration": 1500, "hitSpeed": 50, "damage": 0, "source": "area_effect_objects_evo", "tidRadius": "TID_TESLAPULSE_RADIUS", "tidDuration": "TID_TESLABUFF_PULSE", "tidDamage": "TID_TESLAPULSE_DAMAGE", "onHitActionData": {"name": "Tesla_EV1_SpawnBuff", "spawnTime": 500, "source": "actions", "tidDamage": "TID_TESLAPULSE_DAMAGE", "tidDuration": "TID_TESLABUFF_PULSE", "spawnDataData": {"name": "Tesla_EV1_WithDamage", "rarity": "Common", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "damagePerSecond": 58, "crownTowerDamagePercent": -70, "hitFrequency": -1, "source": "character_buffs_evo"}}}}}}, "summonCharacterData": {"name": "Tesla", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "hitpoints": 450, "hitSpeed": 1100, "loadTime": 700, "damage": 86, "range": 5500, "lifeTime": 30000, "collisionRadius": 500, "tid": "TID_BUILDING_TESLA", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "englishName": "Tesla"}, {"name": "<PERSON><PERSON><PERSON>", "iconFile": "building_elixir_collector", "unlockArena": "Arena_L1", "rarity": "Rare", "manaCost": 6, "tid": "TID_SPELL_ELIXIR_COLLECTOR", "tidInfo": "TID_SPELL_INFO_ELIXIR_COLLECTOR", "id": 27000007, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "Elixir<PERSON><PERSON><PERSON>", "rarity": "Rare", "deployTime": 1000, "hitpoints": 505, "lifeTime": 86000, "collisionRadius": 1000, "tid": "TID_BUILDING_ELIXIR_COLLECTOR", "manaCollectAmount": 1, "manaGenerateTimeMs": 12000, "manaOnDeath": 1, "source": "buildings"}, "englishName": "<PERSON><PERSON><PERSON>"}, {"name": "Xbow", "iconFile": "building_xbow", "unlockArena": "Arena_L", "rarity": "Epic", "manaCost": 6, "tid": "TID_SPELL_XBOW", "tidInfo": "TID_SPELL_INFO_XBOW", "id": 27000008, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "Xbow", "rarity": "Epic", "sightRange": 11500, "deployTime": 3500, "hitpoints": 1000, "hitSpeed": 300, "range": 11500, "lifeTime": 30000, "collisionRadius": 600, "tid": "TID_BUILDING_XBOW", "source": "buildings", "tidTarget": "TID_TARGETS_GROUND", "projectileData": {"name": "xbow_projectile", "rarity": "Epic", "speed": 1400, "damage": 26, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "X-Bow"}, {"name": "Tombstone", "iconFile": "tombstone", "highresImageFilename": "image/chr/tombstone_dl.png", "unlockArena": "Arena2", "rarity": "Rare", "manaCost": 3, "tid": "TID_SPELL_TOMBSTONE", "tidInfo": "TID_SPELL_INFO_TOMBSTONE", "id": 27000009, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "Tombstone", "rarity": "Rare", "deployTime": 1000, "hitpoints": 250, "hitSpeed": 10000, "lifeTime": 30000, "collisionRadius": 1000, "spawnInterval": 500, "spawnNumber": 2, "spawnPauseTime": 3500, "deathSpawnCount": 4, "tid": "TID_BUILDING_TOMBSTONE", "source": "buildings", "spawnCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "deathSpawnCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}, "englishName": "Tombstone"}, {"name": "FirespiritHut", "iconFile": "firespirit_hut", "unlockArena": "Arena_L", "rarity": "Rare", "manaCost": 4, "tid": "TID_SPELL_FIRE_SPIRIT_HUT", "tidInfo": "TID_SPELL_INFO_FIRE_SPIRIT_HUT", "id": 27000010, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "FirespiritHut", "rarity": "Rare", "deployTime": 1000, "hitpoints": 400, "hitSpeed": 6000, "lifeTime": 28000, "collisionRadius": 1000, "spawnNumber": 1, "spawnPauseTime": 5000, "deathSpawnCount": 1, "tid": "TID_BUILDING_FIRE_SPIRIT_HUT", "source": "buildings", "spawnCharacterData": {"name": "FireSpirits", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 90, "hitSpeed": 300, "loadTime": 100, "range": 2000, "collisionRadius": 400, "kamikaze": true, "tid": "TID_CHARACTER_FIRE_SPIRITS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "FireSpiritsProjectile", "rarity": "Common", "speed": 400, "damage": 81, "radius": 2300, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}, "deathSpawnCharacterData": {"name": "FireSpirits", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 90, "hitSpeed": 300, "loadTime": 100, "range": 2000, "collisionRadius": 400, "kamikaze": true, "tid": "TID_CHARACTER_FIRE_SPIRITS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "FireSpiritsProjectile", "rarity": "Common", "speed": 400, "damage": 81, "radius": 2300, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}}}, "englishName": "Furnace"}, {"name": "BarbarianLauncher", "iconFile": "barbarian_launcher", "highresImageFilename": "image/chr/mortar_dl.png", "unlockArena": "Arena6", "rarity": "Common", "notVisible": true, "manaCost": 5, "tid": "TID_SPELL_BARBARIAN_LAUNCHER", "tidInfo": "TID_SPELL_INFO_MORTAR", "id": 27000011, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "BarbarianLauncher", "rarity": "Common", "sightRange": 11500, "deployTime": 3500, "hitpoints": 575, "hitSpeed": 3000, "loadTime": 2000, "range": 11500, "lifeTime": 30000, "collisionRadius": 600, "tid": "TID_BUILDING_MORTAR", "source": "buildings", "tidTarget": "TID_TARGETS_GROUND", "projectileData": {"name": "BarbarianLauncherProjectile", "rarity": "Common", "speed": 300, "damage": 1, "radius": 2000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND", "spawnCharacterData": {"name": "Barbarian", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 262, "hitSpeed": 1300, "loadTime": 900, "damage": 75, "range": 700, "collisionRadius": 500, "tid": "TID_CHARACTER_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}}, "englishName": "Barbarian Launcher"}, {"name": "GoblinCage", "iconFile": "goblin_cage", "highresImageFilename": "image/chr/goblin_cage.png", "unlockArena": "Arena1", "rarity": "Rare", "manaCost": 4, "tid": "TID_SPELL_GOBLINCAGE", "tidInfo": "TID_SPELL_INFO_GOBLINCAGE", "id": 27000012, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "evolvedSpellsData": {"name": "GoblinCage_EV1", "iconFile": "goblin_cage_evolution_card", "highresImageFilename": "image/chr_evolution/goblin_cage_evolution_dl.png", "unlockArena": "Arena_L10", "rarity": "Rare", "manaCost": 4, "summonNumber": 1, "tid": "TID_SPELL_GOBLINCAGE", "tidInfo": "TID_SPELL_INFO_EVO_GOBLINCAGE", "source": "spells_evolved", "summonCharacterData": {"fileName": "sc/chr_goblin_cage_evolution.sc", "blueExportName": "cage_goblin_evolution_blue", "blueTopExportName": "cage_goblin_evolution_blue_top", "redExportName": "cage_goblin_evolution_red", "redTopExportName": "cage_goblin_evolution_red_top", "healthBar": "Medium_EV1", "healthBarOffsetY": -14, "scale": 52, "shadowScaleX": 95, "shadowScaleY": 20, "shadowY": 36, "shadowSkew": 14, "clonedVersion": "GoblinCage", "spawnEffect": "Deploy_Goblin_Cage_Evolution", "customSpawnFilter": "filter_card_evolution", "statsTags": {"hitpoints": "hitpoints", "deathSpawnCount": "spawn_count", "lifeTime": "lifetime", "level": "level"}, "name": "GoblinCage_EV1_TEMPNAME", "source": "ext", "hitpoints": 368, "sightRange": 20000, "range": 5000, "hitSpeed": 10000, "baseData": {"name": "GoblinCage", "rarity": "Rare", "sightRange": 20000, "deployTime": 1000, "hitpoints": 368, "hitSpeed": 10000, "lifeTime": 20000, "collisionRadius": 1000, "deathSpawnCount": 1, "tid": "TID_BUILDING_GOBLINCAGE", "source": "buildings", "deathSpawnCharacterData": {"name": "GoblinBrawler", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 510, "hitSpeed": 1100, "loadTime": 900, "damage": 159, "range": 800, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLINBRAWLER", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}, "onStartingActionData": {"name": "Goblin_cage_starting_actions", "source": "actions", "subActionsData": [{"captureRadius": 3000, "hitFrequency": 1000, "damagePerHit": 159, "numberOfUnitsToCapture": 1, "targetFilter": "GroundCharacterTargetsNoBuildings", "name": "GoblinCage_EV1_CaptureUnit", "source": "actions"}, {"name": "goblin_cage_shake_when_target", "source": "actions"}]}, "deathSpawnCharacterData": {"tid": "TID_CHARACTER_EVOLVED_GOBLINBRAWLER", "hitpoints": 561, "fileName": "sc/chr_goblin_cage_evolution.sc", "blueExportName": "evo_goblincage", "redExportName": "evo_goblincage_enemy", "useAnimator": true, "scale": 70, "shadowScaleX": 85, "shadowScaleY": 60, "shadowY": 10, "shadowSkew": 25, "spawnEffect": "Spawn_Goblin_Brawler_Evolution", "deathEffect": "Death_card_evolution_M", "healthBarOffsetY": -8, "statsTags": {"tid": "spawn_tid", "hitpoints": "spawn_hitpoints", "damage": "spawn_damage", "hitSpeed": "spawn_hit_speed", "speed": "spawn_speed", "range": "spawn_range", "targets": "spawn_targets"}, "name": "GoblinCage_EV1_GoblinBrawler", "source": "ext", "sightRange": 5500, "range": 800, "loadTime": 900, "hitSpeed": 1100, "baseData": {"name": "GoblinBrawler", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 510, "hitSpeed": 1100, "loadTime": 900, "damage": 159, "range": 800, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLINBRAWLER", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}}}, "summonCharacterData": {"name": "GoblinCage", "rarity": "Rare", "sightRange": 20000, "deployTime": 1000, "hitpoints": 368, "hitSpeed": 10000, "lifeTime": 20000, "collisionRadius": 1000, "deathSpawnCount": 1, "tid": "TID_BUILDING_GOBLINCAGE", "source": "buildings", "deathSpawnCharacterData": {"name": "GoblinBrawler", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 510, "hitSpeed": 1100, "loadTime": 900, "damage": 159, "range": 800, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLINBRAWLER", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}, "englishName": "Goblin <PERSON>"}, {"name": "GoblinDrill", "iconFile": "goblindrill", "highresImageFilename": "image/chr/goblin_drill_dl.png", "unlockArena": "Arena14", "rarity": "Epic", "manaCost": 4, "spellAsDeploy": true, "tid": "TID_SPELL_GOBLIN_DRILL", "tidInfo": "TID_SPELL_INFO_GOBLIN_DRILL", "id": 27000013, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "evolvedSpellsData": {"fileName": "sc/building_goblin_drill_evolution.sc", "blueExportName": "goblin_drill_evolution", "redExportName": "goblin_drill_evolution_enemy", "healthBar": "High_3_Segments_EV1", "scale": 110, "spawnPathfindEffect": "goblin_drill_trail_effect_evolution", "deathEffect": "goblin_drill_evolution_die", "clonedVersion": "GoblinDrill", "name": "GoblinDrill_EV1", "source": "ext", "hitpoints": 820, "sightRange": 2000, "range": 2000, "loadTime": 200, "hitSpeed": 1100, "baseData": {"name": "GoblinDrill", "rarity": "Epic", "sightRange": 2000, "deployTime": 1000, "hitpoints": 820, "hitSpeed": 1100, "loadTime": 200, "lifeTime": 10000, "collisionRadius": 500, "spawnNumber": 1, "spawnPauseTime": 3000, "deathSpawnCount": 2, "source": "buildings", "spawnCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}, "deathSpawnCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}, "spawnAreaObjectData": {"name": "GoblinDrillDamage", "rarity": "Epic", "lifeDuration": 1, "radius": 2000, "damage": 52, "crownTowerDamagePercent": -70, "onlyEnemies": true, "hitsGround": true, "hitsAir": false, "source": "area_effect_objects"}}, "onStartingActionData": {"name": "GoblinDrill_EV1_start", "source": "actions", "subActionsData": [{"hideTime": 1000, "spawnCharacterOnHide": "Goblin", "spawnCharacterOnHideCounts": [1, 1], "hideHpThresholds": [66, 33], "name": "GoblinDrill_EV1_relocate", "source": "actions"}, {"name": "GoblinDrill_EV1_appear_effect", "source": "actions"}]}}, "summonCharacterData": {"name": "GoblinDrillDig", "rarity": "Epic", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 1000, "hitSpeed": 1200, "loadTime": 700, "collisionRadius": 0, "tid": "TID_SPELL_KNIGHT", "source": "characters", "tidSpeed": "TID_SPEED_3", "spawnPathfindMorphData": {"name": "GoblinDrill", "rarity": "Epic", "sightRange": 2000, "deployTime": 1000, "hitpoints": 820, "hitSpeed": 1100, "loadTime": 200, "lifeTime": 10000, "collisionRadius": 500, "spawnNumber": 1, "spawnPauseTime": 3000, "deathSpawnCount": 2, "source": "buildings", "spawnCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}, "deathSpawnCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}, "spawnAreaObjectData": {"name": "GoblinDrillDamage", "rarity": "Epic", "lifeDuration": 1, "radius": 2000, "damage": 52, "crownTowerDamagePercent": -70, "onlyEnemies": true, "hitsGround": true, "hitsAir": false, "source": "area_effect_objects"}}}, "englishName": "<PERSON>blin <PERSON>ill"}, {"name": "GoblinPartyHut", "iconFile": "PartyHut", "highresImageFilename": "image/chr/goblinhut_dl.png", "unlockArena": "Arena_L", "rarity": "Legendary", "notVisible": true, "manaCost": 5, "tid": "TID_SPELL_GOBLIN_HUT_PARTY", "tidInfo": "TID_SPELL_INFO_GOBLIN_HUT", "id": 27000014, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"name": "GoblinPartyHut", "rarity": "Legendary", "sightRange": 11000, "deployTime": 3500, "hitpoints": 704, "hitSpeed": 700, "loadTime": 350, "range": 11000, "lifeTime": 20000, "collisionRadius": 1000, "deathSpawnCount": 3, "deathSpawnCount2": 1, "tid": "TID_BUILDING_GOBLIN_HUT", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "deathSpawnCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 110, "hitSpeed": 1700, "loadTime": 1200, "range": 5000, "collisionRadius": 500, "tid": "TID_CHARACTER_SPEAR_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "SpearGoblinPartyProjectile", "rarity": "Legendary", "speed": 500, "damage": 67, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "deathSpawnCharacter2Data": {"name": "GoblinBrawlerParty", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 853, "hitSpeed": 1100, "loadTime": 900, "damage": 279, "range": 800, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLINBRAWLER", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}, "projectileData": {"name": "BlowdartGoblinProjectileParty", "rarity": "Legendary", "speed": 800, "damage": 109, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}, "deathSpawnProjectileData": {"name": "GoblinProjectile", "rarity": "Legendary", "speed": 100, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "spawnCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}}}, "englishName": "Party Hut"}, {"name": "GoblinRocketSilo", "iconFile": "PartyRocket", "unlockArena": "Arena_L", "rarity": "Legendary", "notVisible": true, "manaCost": 7, "tid": "TID_SPELL_ROCKET_SILO", "tidInfo": "TID_SPELL_ROCKET_SILO", "id": 27000017, "tidType": "TID_CARD_TYPE_BUILDING", "source": "spells_buildings", "summonCharacterData": {"rarity": "Common", "deployTime": 1000, "hitpoints": 781, "sightRange": 25000, "collisionRadius": 600, "tid": "TID_BUILDING_CANNON", "name": "GoblinRocketSilo", "source": "buildings"}, "englishName": "Rocket Silo"}, {"name": "Fireball", "iconFile": "fire_fireball", "unlockArena": "TrainingCamp", "rarity": "Rare", "manaCost": 4, "tid": "TID_SPELL_FIREBALL", "tidInfo": "TID_SPELL_INFO_FIREBALL", "id": 28000000, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "FireballSpell", "rarity": "Rare", "speed": 600, "damage": 325, "crownTowerDamagePercent": -70, "pushback": 1000, "radius": 2500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "englishName": "Fireball"}, {"name": "Arrows", "iconFile": "order_volley", "highresImageFilename": "image/chr/arrows.png", "unlockArena": "TrainingCamp", "rarity": "Common", "manaCost": 3, "radius": 4000, "projectileWaves": 3, "projectileWaveInterval": 200, "multipleProjectiles": 10, "tid": "TID_SPELL_ARROWS", "tidInfo": "TID_SPELL_INFO_ARROWS", "id": 28000001, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "ArrowsSpell", "rarity": "Common", "speed": 1100, "damage": 48, "crownTowerDamagePercent": -75, "radius": 1400, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "englishName": "Arrows"}, {"name": "Rage", "iconFile": "rage", "unlockArena": "Arena14", "rarity": "Epic", "manaCost": 2, "radius": 3000, "tid": "TID_SPELL_RAGE", "tidInfo": "TID_SPELL_INFO_RAGE", "id": 28000002, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "summonCharacterData": {"name": "RageBottle", "rarity": "Epic", "deployTime": 500, "source": "buildings", "deathAreaEffectData": {"name": "Rage", "rarity": "Epic", "lifeDuration": 5500, "radius": 3000, "hitSpeed": 300, "buffTime": 2000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "Rage", "rarity": "Epic", "tid": "TID_SPELL_RAGE", "hitSpeedMultiplier": 135, "speedMultiplier": 135, "spawnSpeedMultiplier": 135, "source": "character_buffs"}, "spawnAreaEffectObjectData": {"name": "RageDamage", "rarity": "Epic", "lifeDuration": 1, "radius": 3000, "damage": 120, "crownTowerDamagePercent": -70, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects"}}}, "englishName": "Rage"}, {"name": "Rocket", "iconFile": "rocket", "highresImageFilename": "image/chr/rocket_dl.png", "unlockArena": "Arena5", "rarity": "Rare", "manaCost": 6, "tid": "TID_SPELL_ROCKET", "tidInfo": "TID_SPELL_INFO_ROCKET", "id": 28000003, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "RocketSpell", "rarity": "Rare", "speed": 350, "damage": 700, "crownTowerDamagePercent": -75, "pushback": 1800, "radius": 2000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND"}, "englishName": "Rocket"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iconFile": "goblin_barrel", "highresImageFilename": "image/chr/goblin_barrel_dl.png", "unlockArena": "Arena6", "rarity": "Epic", "manaCost": 3, "tid": "TID_SPELL_GOBLIN_BARREL", "tidInfo": "TID_SPELL_INFO_GOBLIN_BARREL", "id": 28000004, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "GoblinBarrelSpell", "rarity": "Epic", "speed": 400, "radius": 1500, "spawnCharacterCount": 3, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnCharacterData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}}, "evolvedSpellsData": {"name": "GoblinBarrel_EV1", "iconFile": "goblin_barrel_evolution_card", "highresImageFilename": "image/chr_evolution/goblin_barrel_evolution_dl.png", "unlockArena": "Arena6", "rarity": "Epic", "manaCost": 3, "tid": "TID_SPELL_GOBLIN_BARREL", "tidInfo": "TID_SPELL_INFO_EVO_GOBLIN_BARREL", "source": "spells_evolved", "projectileData": {"name": "GoblinBarrelSpell_EV1", "base": "GoblinBarrelSpell", "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "decoyData": {"name": "GoblinBarrelSpell_EV1_Decoy", "base": "GoblinBarrelSpell", "source": "projectiles_evo", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnCharacterData": {"name": "GoblinDummy", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 32, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN_DUMMY", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}}}}, "englishName": "<PERSON><PERSON>"}, {"name": "Freeze", "iconFile": "freeze", "unlockArena": "Arena8", "rarity": "Epic", "manaCost": 4, "tid": "TID_SPELL_FREEZE", "tidInfo": "TID_SPELL_INFO_FREEZE", "id": 28000005, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "Freeze", "rarity": "Epic", "lifeDuration": 4000, "radius": 3000, "damage": 72, "crownTowerDamagePercent": -70, "buffTime": 4000, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "Freeze", "rarity": "Common", "tid": "TID_SPELL_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}, "englishName": "Freeze"}, {"name": "Mirror", "iconFile": "mirror", "highresImageFilename": "image/chr/mirror_dl.png", "unlockArena": "Arena_L1", "rarity": "Epic", "manaCost": 1, "tid": "TID_SPELL_MIRROR", "tidInfo": "TID_SPELL_INFO_MIRROR", "id": 28000006, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "englishName": "Mirror"}, {"name": "Lightning", "iconFile": "lightning", "unlockArena": "Arena8", "rarity": "Epic", "manaCost": 6, "radius": 3500, "tid": "TID_SPELL_LIGHTNING", "tidInfo": "TID_SPELL_INFO_LIGHTNING", "id": 28000007, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "Lightning", "rarity": "Epic", "lifeDuration": 1500, "radius": 3500, "hitSpeed": 460, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "projectileData": {"name": "LighningSpell", "rarity": "Epic", "speed": 500, "damage": 660, "crownTowerDamagePercent": -70, "buffTime": 500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_DAMAGE", "targetBuffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}, "englishName": "Lightning"}, {"name": "Zap", "iconFile": "zap", "highresImageFilename": "image/chr/zap_dl.png", "unlockArena": "Arena5", "rarity": "Common", "manaCost": 2, "radius": 2500, "tid": "TID_SPELL_ZAP", "tidInfo": "TID_SPELL_INFO_ZAP", "id": 28000008, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "evolvedSpellsData": {"name": "Zap_EV1", "iconFile": "zap_evolution_card", "highresImageFilename": "image/chr_evolution/zap_evolution_dl.png", "unlockArena": "Arena5", "rarity": "Common", "manaCost": 2, "tid": "TID_SPELL_ZAP", "tidInfo": "TID_SPELL_INFO_EVO_ZAP", "source": "spells_evolved", "areaEffectObjectData": {"name": "Zap_EV1", "base": "Zap", "lifeDuration": 5000, "hitSpeed": 0, "source": "area_effect_objects_evo", "radius": 2500, "buffTime": 500, "onStartingActionData": {"name": "Zap_EV1_AfterStun_V2", "source": "actions", "tidAction": "TID_ZAPEVO_COUNT", "subActionsData": [{"name": "DummyZap_EV1_Visual", "rarity": "Common", "hitSpeed": 0, "lifeDuration": 1500, "source": "area_effect_objects_evo"}, {"name": "Zap_EV1_SpawnAOE_medium", "base": "Zap", "radius": 3000, "lifeDuration": 400, "source": "area_effect_objects_evo", "buffTime": 500}]}}}, "areaEffectObjectData": {"name": "Zap", "rarity": "Common", "lifeDuration": 1, "radius": 2500, "damage": 75, "crownTowerDamagePercent": -70, "buffTime": 500, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}, "englishName": "Zap"}, {"name": "Poison", "iconFile": "poison", "unlockArena": "Arena9", "rarity": "Epic", "manaCost": 4, "radius": 3500, "tid": "TID_SPELL_POISON", "tidInfo": "TID_SPELL_INFO_POISON", "id": 28000009, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "Poison", "rarity": "Epic", "lifeDuration": 8000, "radius": 3500, "hitSpeed": 250, "buffTime": 1000, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "Poison", "rarity": "Epic", "tid": "TID_SPELL_POISON", "crownTowerDamagePercent": -75, "damagePerSecond": 57, "hitFrequency": 1000, "speedMultiplier": -15, "source": "character_buffs"}}, "englishName": "Poison"}, {"name": "Graveyard", "iconFile": "graveyard", "highresImageFilename": "image/chr/graveyard_dl.png", "unlockArena": "Arena_T", "rarity": "Legendary", "manaCost": 5, "radius": 4000, "tid": "TID_SPELL_GRAVEYARD", "tidInfo": "TID_SPELL_INFO_GRAVEYARD", "id": 28000010, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "Graveyard", "rarity": "Legendary", "lifeDuration": 9000, "radius": 4000, "spawnInterval": 500, "spawnTime": 400, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "spawnCharacterData": {"name": "Skeleton", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 90, "hitpoints": 32, "hitSpeed": 1000, "loadTime": 500, "damage": 32, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_SKELETON", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_4"}}, "englishName": "Graveyard"}, {"name": "Log", "iconFile": "the_log", "highresImageFilename": "image/chr/the_log_dl.png", "unlockArena": "Arena_Electric", "rarity": "Legendary", "manaCost": 2, "spellAsDeploy": true, "tid": "TID_SPELL_LOG", "tidInfo": "TID_SPELL_INFO_LOG", "id": 28000011, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "LogProjectile", "rarity": "Legendary", "speed": 360, "radius": 1950, "radiusY": 600, "spawnChain": 1, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnProjectileData": {"name": "LogProjectileRolling", "rarity": "Legendary", "speed": 200, "damage": 240, "crownTowerDamagePercent": -80, "pushback": 700, "projectileRange": 10100, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND"}}, "englishName": "The Log"}, {"name": "Tornado", "iconFile": "tornado", "highresImageFilename": "image/chr/tornado_dl.png", "unlockArena": "Arena_L1", "rarity": "Epic", "manaCost": 3, "radius": 5500, "spellAsDeploy": true, "tid": "TID_SPELL_TORNADO", "tidInfo": "TID_SPELL_INFO_TORNADO", "id": 28000012, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "Tornado", "rarity": "Epic", "lifeDuration": 1050, "radius": 5500, "hitSpeed": 50, "buffTime": 500, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "Tornado", "rarity": "Epic", "tid": "TID_SPELL_TORNADO", "crownTowerDamagePercent": -83, "damagePerSecond": 106, "hitFrequency": 500, "source": "character_buffs"}}, "englishName": "Tornado"}, {"name": "<PERSON><PERSON>", "iconFile": "copy", "highresImageFilename": "image/chr/clone_dl.png", "unlockArena": "Arena_L1", "rarity": "Epic", "manaCost": 3, "radius": 3000, "tid": "TID_SPELL_CLONE", "tidInfo": "TID_SPELL_INFO_CLONE", "id": 28000013, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "<PERSON><PERSON>", "rarity": "Epic", "lifeDuration": 1000, "radius": 3000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "onHitActionData": {"name": "CloneAction", "source": "actions"}}, "englishName": "<PERSON><PERSON>"}, {"name": "Earthquake", "iconFile": "earthquake", "highresImageFilename": "image/chr/earthquake_dl.png", "unlockArena": "Arena_T", "rarity": "Rare", "manaCost": 3, "radius": 3500, "tid": "TID_SPELL_EARTHQUAKE", "tidInfo": "TID_SPELL_INFO_EARTHQUAKE", "id": 28000014, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "Earthquake", "rarity": "Rare", "lifeDuration": 3000, "radius": 3500, "hitSpeed": 100, "buffTime": 1000, "onlyEnemies": true, "hitsGround": true, "hitsAir": false, "source": "area_effect_objects", "buffData": {"name": "Earthquake", "rarity": "Rare", "buildingDamagePercent": 350, "crownTowerDamagePercent": -35, "damagePerSecond": 39, "hitFrequency": 1000, "speedMultiplier": -50, "source": "character_buffs"}}, "englishName": "Earthquake"}, {"name": "BarbLog", "iconFile": "barb_barrel", "highresImageFilename": "image/chr/barbarian_barrel_dl.png", "unlockArena": "Arena9", "rarity": "Epic", "manaCost": 2, "spellAsDeploy": true, "tid": "TID_SPELL_BARBARIAN_LOG", "tidInfo": "TID_SPELL_INFO_BARBARIAN_LOG", "id": 28000015, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "BarbLogProjectile", "rarity": "Epic", "speed": 360, "radius": 1300, "radiusY": 600, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "spawnProjectileData": {"name": "BarbLogProjectileRolling", "rarity": "Epic", "speed": 200, "damage": 151, "pushback": 0, "projectileRange": 4500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_GROUND", "spawnCharacterData": {"name": "Barbarian", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 262, "hitSpeed": 1300, "loadTime": 900, "damage": 75, "range": 700, "collisionRadius": 500, "tid": "TID_CHARACTER_BARBARIAN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}}, "englishName": "Barbarian Barrel"}, {"name": "Heal", "iconFile": "<PERSON><PERSON><PERSON><PERSON>", "highresImageFilename": "image/chr/heal_spirit_dl.png", "unlockArena": "Arena13", "rarity": "Rare", "manaCost": 1, "spellAsDeploy": true, "tid": "TID_SPELL_HEAL_SPIRIT", "tidInfo": "TID_SPELL_INFO_HEAL_SPIRIT", "id": 28000016, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "summonCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Rare", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 109, "hitSpeed": 300, "loadTime": 100, "range": 2500, "collisionRadius": 400, "kamikaze": true, "tid": "TID_CHARACTER_ICE_SPIRITS", "source": "characters", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "tidSpeed": "TID_SPEED_5", "projectileData": {"name": "HealSpiritProjectile", "rarity": "Rare", "speed": 400, "damage": 52, "radius": 1500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "spawnAreaEffectObjectData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Rare", "lifeDuration": 1000, "radius": 2500, "hitSpeed": 0, "buffTime": 1000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Rare", "hitFrequency": 250, "healPerSecond": 189, "source": "character_buffs"}}}}, "englishName": "Heal Spirit"}, {"name": "Snowball", "iconFile": "snowball", "unlockArena": "Arena8", "rarity": "Common", "manaCost": 2, "tid": "TID_SPELL_SNOWBALL", "tidInfo": "TID_SPELL_INFO_SNOWBALL", "id": 28000017, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "SnowballSpell", "rarity": "Common", "speed": 800, "damage": 70, "crownTowerDamagePercent": -70, "pushback": 1800, "radius": 2500, "buffTime": 3000, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "targetBuffData": {"name": "IceWizardSlowDown", "rarity": "Common", "tid": "TID_SPELL_ATTRIBUTE_SLOWDOWN", "hitSpeedMultiplier": -35, "speedMultiplier": -35, "spawnSpeedMultiplier": -35, "source": "character_buffs"}}, "evolvedSpellsData": {"name": "Snowball_EV1", "iconFile": "snowball_evolution_card", "highresImageFilename": "image/chr_evolution/snowball_evolution_dl.png", "unlockArena": "Arena8", "rarity": "Common", "manaCost": 2, "projectile": "SnowballSpell_EV1", "tid": "TID_SPELL_SNOWBALL", "tidInfo": "TID_SPELL_INFO_EVO_SNOWBALL", "source": "spells_evolved"}, "englishName": "Giant Snowball"}, {"name": "RoyalDelivery", "iconFile": "royal_delivery", "highresImageFilename": "image/chr/royal_delivery_dl.png", "unlockArena": "Arena14", "rarity": "Common", "manaCost": 3, "radius": 3000, "spellAsDeploy": true, "tid": "TID_SPELL_ROYAL_DELIVERY", "tidInfo": "TID_SPELL_INFO_ROYAL_DELIVERY", "id": 28000018, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "RoyalDeliveryArea", "rarity": "Common", "lifeDuration": 2000, "radius": 3000, "hitSpeed": 2000, "onlyEnemies": true, "spawnTime": 250, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "projectileData": {"name": "RoyalDeliveryProjectile", "rarity": "Common", "speed": 5000, "damage": 171, "radius": 3000, "spawnCharacterCount": 1, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_AREA_DAMAGE", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "spawnCharacterData": {"name": "DeliveryRecruit", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 60, "hitpoints": 214, "hitSpeed": 1300, "loadTime": 800, "damage": 52, "range": 1600, "collisionRadius": 500, "tid": "TID_SPELL_GUARD_BATTALION", "shieldHitpoints": 94, "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_3"}}}, "englishName": "Royal Delivery"}, {"name": "GlobalClone", "iconFile": "copy", "highresImageFilename": "image/chr/clone_dl.png", "unlockArena": "Arena_L1", "rarity": "Epic", "notVisible": true, "manaCost": 3, "radius": 30000, "tid": "TID_SPELL_CLONE", "tidInfo": "TID_SPELL_INFO_CLONE", "id": 28000019, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "GlobalClone", "rarity": "Epic", "lifeDuration": 1000, "radius": 30000, "buffTime": 500, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "<PERSON><PERSON>", "rarity": "Epic", "tid": "TID_SPELL_CLONE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}, "onHitActionData": {"name": "CloneAction", "source": "actions"}}, "englishName": "<PERSON><PERSON>"}, {"name": "GoblinPartyRocket", "iconFile": "PartyRocket", "highresImageFilename": "image/chr/rocket_dl.png", "unlockArena": "Arena_L1", "rarity": "Legendary", "notVisible": true, "manaCost": 5, "tid": "TID_SPELL_GOBLIN_PARTY_ROCKET", "tidInfo": "TID_SPELL_INFO_ROCKET", "id": 28000020, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "projectileData": {"name": "GoblinMorphProjectile", "rarity": "Legendary", "speed": 350, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE", "spawnAreaEffectObjectData": {"name": "GoblinMorphAOEFriend", "rarity": "Legendary", "lifeDuration": 1000, "radius": 4000, "buffTime": 2000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "GoblinCurseFriend", "rarity": "Legendary", "deathSpawnCount": 1, "source": "character_buffs", "deathSpawnData": {"name": "GoblinParty", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 167, "hitSpeed": 1100, "loadTime": 700, "damage": 99, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}}, "spawnAreaEffectObjectData": {"name": "GoblinMorphAOEFoe", "rarity": "Legendary", "lifeDuration": 1000, "radius": 4000, "buffTime": 2000, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "GoblinCurseFoe", "rarity": "Legendary", "deathSpawnCount": 1, "source": "character_buffs", "deathSpawnData": {"name": "GoblinParty", "rarity": "Legendary", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 167, "hitSpeed": 1100, "loadTime": 700, "damage": 99, "range": 500, "collisionRadius": 500, "tid": "TID_CHARACTER_GOBLIN", "source": "characters", "tidTarget": "TID_TARGETS_GROUND", "tidSpeed": "TID_SPEED_5"}}, "spawnAreaEffectObjectData": {"name": "GoblinMorphAOEDamage", "rarity": "Legendary", "lifeDuration": 1, "radius": 4000, "damage": 9999, "crownTowerDamagePercent": -100, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects"}}}}, "englishName": "Party Rocket"}, {"name": "WarmSpell", "iconFile": "warmth_spell", "unlockArena": "Arena_L1", "rarity": "Common", "notVisible": true, "manaCost": 1, "radius": 4000, "tid": "TID_SPELL_WARM", "tidInfo": "TID_SPELL_INFO_WARM", "id": 28000021, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "WarmAOE", "rarity": "Common", "lifeDuration": 1000, "radius": 4000, "hitSpeed": 500, "buffTime": 1000, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "buffData": {"name": "WarmUp", "rarity": "Common", "hitFrequency": 550, "healPerSecond": 10, "source": "character_buffs"}}, "englishName": "Warmth"}, {"name": "GlobalLightning", "iconFile": "lightning", "unlockArena": "Arena8", "rarity": "Epic", "notVisible": true, "manaCost": 1, "tid": "TID_SPELL_LIGHTNING", "tidInfo": "TID_SPELL_INFO_LIGHTNING", "id": 28000022, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "Event_Global_Lightning_Charge1", "rarity": "Epic", "lifeDuration": 5000, "damage": 10, "source": "area_effect_objects", "onStartingActionData": {"name": "GlobalLightningSpawnCharge2", "source": "actions", "spawnDataData": {"name": "Event_Global_Lightning_Charge2", "rarity": "Epic", "lifeDuration": 5000, "damage": 10, "source": "area_effect_objects", "onStartingActionData": {"name": "GlobalLightning", "source": "actions", "spawnDataData": {"name": "Event_Global_Lightning", "rarity": "Epic", "lifeDuration": 3000, "radius": 40000, "hitSpeed": 950, "hitsGround": true, "hitsAir": true, "source": "area_effect_objects", "projectileData": {"name": "LighningSpell", "rarity": "Epic", "speed": 500, "damage": 660, "crownTowerDamagePercent": -70, "buffTime": 500, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_DAMAGE", "targetBuffData": {"name": "ZapFreeze", "rarity": "Common", "tid": "TID_SPELL_ZAP_FREEZE", "hitSpeedMultiplier": -100, "speedMultiplier": -100, "spawnSpeedMultiplier": -100, "source": "character_buffs"}}}}}}}, "englishName": "Lightning"}, {"name": "DarkMagic", "iconFile": "darkspell", "highresImageFilename": "image/chr/dark_spell_dl.png", "unlockArena": "Arena_L1", "rarity": "Epic", "manaCost": 3, "radius": 2500, "tid": "TID_SPELL_DARK_MAGIC", "tidInfo": "TID_SPELL_INFO_DARK_MAGIC", "id": 28000023, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"name": "DarkMagicAOE", "rarity": "Epic", "lifeDuration": 4900, "radius": 2500, "hitSpeed": -1, "damage": 100, "onlyEnemies": true, "hitsGround": false, "hitsAir": false, "onStartingAction": {"classType": "ActionGroup", "subActionsDelay": [0, 500], "subActions": [{"classType": "ActionPlayEffect", "effect": "PaoloFirstEffectEverIntro"}, {"classType": "ActionLaserBall", "detectionRadius": 2500, "firstHitDelay": 1000, "hitFrequency": 1300, "maxUnitPerActionList": [1, 4], "mainEffectList": ["PaoloFirstEffectEver"], "hitFilter": "ForcedCharacterTargets", "statsTags": {"minUnits_2": "targets_mid_lower", "maxUnits_2": "targets_mid_upper", "minUnits_3": "targets_more_lower"}, "onDetectedUnitActionList": [{"classType": "ActionSpawn", "spawnType": "BuffType", "spawnTime": 100, "spawnData": {"name": "DarkMagicAOE_Damage_lv3", "rarity": "Epic", "damagePerSecond": 2000, "crownTowerDamagePerHit": 30, "hitFrequency": 100, "addAsIndividualBuff": true, "hitEffect": "PaoloFirstEffectEverStrong", "statsTags": {"damagePerHit": "damage_first_target", "crownTowerDamagePerHit": "damage_first_tower_target"}}, "nextAction": {"classType": "ActionPlayEffect", "effect": "<PERSON>_zap_effect3"}}, {"classType": "ActionSpawn", "spawnType": "BuffType", "spawnTime": 100, "spawnData": {"name": "DarkMagicAOE_Damage_lv2", "rarity": "Epic", "damagePerSecond": 1000, "crownTowerDamagePerHit": 15, "hitFrequency": 100, "addAsIndividualBuff": true, "hitEffect": "PaoloFirstEffectEverMedium", "statsTags": {"damagePerHit": "damage_mid_targets", "crownTowerDamagePerHit": "damage_mid_tower_targets"}}, "nextAction": {"classType": "ActionPlayEffect", "effect": "<PERSON>_zap_effect2"}}, {"classType": "ActionSpawn", "spawnType": "BuffType", "spawnTime": 100, "spawnData": {"name": "DarkMagicAOE_Damage_lv1", "rarity": "Epic", "damagePerSecond": 475, "crownTowerDamagePerHit": 10, "hitFrequency": 100, "addAsIndividualBuff": true, "hitEffect": "PaoloFirstEffectEverLow", "statsTags": {"damagePerHit": "damage_more_targets", "crownTowerDamagePerHit": "damage_more_tower_targets"}}, "nextAction": {"classType": "ActionPlayEffect", "effect": "Paolo_zap_effect"}}]}]}, "source": "area_effect_objects"}, "englishName": "Void"}, {"name": "GoblinCurse", "iconFile": "goblincurse", "highresImageFilename": "image/chr_goblin_faction/goblin_curse_dl.png", "unlockArena": "Arena14", "rarity": "Epic", "manaCost": 2, "radius": 3000, "tid": "TID_SPELL_GOBLIN_CURSE", "tidInfo": "TID_SPELL_INFO_GOBLIN_CURSE", "id": 28000024, "tidType": "TID_CARD_TYPE_SPELL", "source": "spells_other", "areaEffectObjectData": {"rarity": "Common", "lifeDuration": 6000, "radius": 3000, "hitSpeed": -1, "damage": 100, "onlyEnemies": true, "hitsGround": false, "hitsAir": false, "name": "GoblinCurse", "source": "area_effect_objects", "onStartingActionData": {"name": "GoblinCurseSet", "source": "actions", "subActionsData": [{"name": "GoblinCurseIntro", "source": "actions"}, {"name": "GoblinCurseCore", "source": "actions", "spawnDataData": {"rarity": "Common", "lifeDuration": 6000, "radius": 3000, "hitSpeed": 50, "onlyEnemies": true, "hitsGround": true, "hitsAir": true, "name": "GoblinCurseBase", "source": "area_effect_objects", "onHitActionData": {"name": "GoblinCurseCreateBuffs", "source": "actions", "subActionsData": [{"rarity": "Common", "tid": "TID_GOBLIN_CURSE", "damageReduction": -20, "name": "GoblinCurseAmplify", "source": "character_buffs"}, {"rarity": "Common", "tid": "TID_GOBLIN_CURSE", "deathSpawnCount": 1, "name": "GoblinCurse", "source": "character_buffs", "deathSpawnData": {"deathEffect": "Death_goblin_curse", "name": "GoblinCurseGoblin", "source": "ext", "hitpoints": 79, "sightRange": 5500, "range": 500, "loadTime": 700, "hitSpeed": 1100, "baseData": {"name": "Goblin", "rarity": "Common", "sightRange": 5500, "deployTime": 1000, "speed": 120, "hitpoints": 79, "hitSpeed": 1100, "loadTime": 700, "damage": 47, "range": 500, "attacksGround": true, "fileName": "sc/chr_goblin.sc", "blueExportName": "goblin1", "redExportName": "goblin1_enemy", "prestigeSwf": "sc/chr_prestige4_dl.sc", "prestigeExportName2": "goblin_prestige", "prestigeExportName3": "goblin_prestige2", "useAnimator": true, "damageEffect": "goblins_hit", "deathEffect": "goblins_die", "moveEffect": "goblins_steps", "spawnEffect": "goblins_deploy", "shadowScaleX": 50, "shadowScaleY": 50, "shadowX": -5, "shadowY": 5, "shadowSkew": 35, "scale": 100, "collisionRadius": 500, "mass": 2, "healthBar": "Small", "damageExportName": "filter_damage", "attackStartEffect": "goblins_attack_start", "projectileStartRadius": 350, "projectileStartZ": 350, "tid": "TID_CHARACTER_GOBLIN", "deployDelay": 400, "deployBaseAnimExportName": "filter_deploy_unit_default", "source": "characters"}}}, {"rarity": "Common", "tid": "TID_GOBLIN_CURSE", "crownTowerDamagePercent": -80, "damagePerSecond": 12, "hitFrequency": 1000, "name": "GoblinCurseDamage", "source": "character_buffs"}]}}}, {"name": "GoblinCurseOutro", "source": "actions"}]}}, "englishName": "Goblin Curse"}, {"name": "King_Princess<PERSON><PERSON><PERSON>", "tid": "TID_SUPPORT_CARD_PRINCESS_TOWERS", "tidInfo": "TID_SUPPORT_CARD_INFO_PRINCESS_TOWERS", "rarity": "Common", "unlockArena": "TrainingCamp", "highresImageFilename": "image/chr_support_cards/support_card_hires_princess.png", "iconFile": "support_card_princess", "id": 159000000, "tidType": "TID_TYPE_TOWER_TROOP", "source": "support_cards", "statCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 7500, "hitpoints": 1400, "hitSpeed": 800, "range": 7500, "collisionRadius": 1000, "tid": "TID_BUILDING_ARCHER_TOWER", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "projectileData": {"name": "TowerPrincessProjectile", "rarity": "Common", "speed": 600, "damage": 50, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Tower Princess"}, {"name": "King_CannonTowers", "tid": "TID_SUPPORT_CARD_CANNONEER", "tidInfo": "TID_SUPPORT_CARD_INFO_CANNONEER", "rarity": "Epic", "unlockArena": "Arena8", "highresImageFilename": "image/chr_support_cards/support_card_hires_cannoneer.png", "iconFile": "support_card_cannoneer", "id": 159000001, "tidType": "TID_TYPE_TOWER_TROOP", "source": "support_cards", "statCharacterData": {"name": "Cannoneer", "rarity": "Common", "sightRange": 7500, "hitpoints": 1200, "hitSpeed": 2400, "loadTime": 1600, "range": 7500, "collisionRadius": 1000, "tid": "TID_BUILDING_ARCHER_TOWER", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "projectileData": {"name": "CannoneerProjectile", "rarity": "Common", "speed": 1000, "damage": 151, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Cannoneer"}, {"name": "King_KnifeTowers", "tid": "TID_SUPPORT_CARD_KNIFE_THROWER", "tidInfo": "TID_SUPPORT_CARD_INFO_KNIFE_THROWER", "rarity": "Legendary", "unlockArena": "Arena_Electric", "highresImageFilename": "image/chr_support_cards/support_card_hires_knives_thrower.png", "iconFile": "support_card_knife_thrower", "id": 159000002, "tidType": "TID_TYPE_TOWER_TROOP", "source": "support_cards", "statCharacterData": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Common", "sightRange": 7500, "hitpoints": 1270, "hitSpeed": 450, "range": 7500, "collisionRadius": 1000, "tid": "TID_BUILDING_ARCHER_TOWER", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "onStartingActionData": {"maxChargeCount": 8, "rechargeTime": 900}, "projectileData": {"name": "TowerKnifeThrowerProjectile", "rarity": "Common", "speed": 1000, "damage": 44, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}, "englishName": "Dagger <PERSON>"}, {"name": "King_ChefTowers", "tid": "TID_SUPPORT_CARD_CHEF", "tidInfo": "TID_SUPPORT_CARD_INFO_CHEF", "rarity": "Legendary", "unlockArena": "Arena13", "highresImageFilename": "image/chr_support_cards/support_card_hires_royal_chef.png", "iconFile": "support_card_royal_chef", "id": 159000004, "tidType": "TID_TYPE_TOWER_TROOP", "source": "support_cards", "statCharacterData": {"rarity": "Common", "sightRange": 7500, "hitpoints": 1240, "hitSpeed": 900, "loadTime": 100, "range": 7500, "collisionRadius": 1000, "tid": "TID_BUILDING_ARCHER_TOWER", "name": "ChefTower", "source": "buildings", "tidTarget": "TID_TARGETS_AIR_AND_GROUND", "projectileData": {"rarity": "Common", "speed": 600, "damage": 50, "damageScalingMode": "<PERSON><PERSON><PERSON><PERSON>", "fileName": "sc/chr_royal_chef.sc", "exportName": "Royal_Chef_spatula_export_dir_bot", "redExportName": "Royal_Chef_spatula_export_dir_top", "hitEffect": "royal_chef_spatula_die", "trailEffect": "royal_chef_spatula_emitter", "gravity": 20, "shadowExportName": "Royal_Chef_spatula_shadow_export", "scale": 60, "statsTags": {"damage": "damage"}, "name": "ChefTower_spatula_projectile", "source": "ext", "baseData": {"name": "MusketeerProjectile", "rarity": "Rare", "speed": 1000, "damage": 103, "source": "projectiles", "tid": "TID_SPELL_ATTRIBUTE_RANGED_DAMAGE"}}}, "englishName": "Royal Chef"}]