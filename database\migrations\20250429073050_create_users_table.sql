CREATE TABLE
  `users` (
    `id_Usu` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `Veri<PERSON>do` TINYINT (1) DEFAULT 0,
    `AuthProvider` ENUM ('sistem', 'google') DEFAULT 'sistem' NOT NULL,
    `authId` VARCHAR(255) UNIQUE NOT NULL,
    `Tag` VARCHAR(20) NULL,
    `Avatar` VARCHAR(50) DEFAULT 'defect.webp',
    `Banner` VARCHAR(50) DEFAULT 'defect.webp',
    `Usuario` VARCHAR(15) UNIQUE NOT NULL,
    `Nombre` VARCHAR(15) NOT NULL,
    `Correo` VARCHAR(250) UNIQUE DEFAULT NULL,
    `<PERSON><PERSON>ena` VARCHAR(250),
    `Informacion` VARCHAR(350) DEFAULT NULL,
    `FechaNac` date DEFAULT NULL,
    `Genero` VARCHAR(10) DEFAULT NULL,
    `Estado` VARCHAR(10) DEFAULT 'Activo',
    `PassStrategic` TINYINT (1) DEFAULT 0,
    `Pas<PERSON><PERSON>Ini` DATETIME NULL,
    `Coins` INT DEFAULT 1000 not null,
    `Gems` INT DEFAULT 100 not null,
    `Mazos` VARCHAR(2000) NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;