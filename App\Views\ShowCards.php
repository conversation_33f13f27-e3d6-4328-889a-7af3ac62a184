<?php
$res = [];
for ($i = 0; $i < 4; $i++) {
    ob_start();
    $json = json_decode(file_get_contents(PATH_ROOT . '/App/Data/cards/statCards.json'));
    $jsonMedia = json_decode(file_get_contents(PATH_ROOT . '/App/Data/cards/mediaCards.json'));
    if ($i == 0) { //por default(id)
        usort($json->cards, function ($a, $b) {
            return $a->id - $b->id;
        });
    }
    if ($i == 1) { //por elixir
        usort($json->cards, function ($a, $b) {
            return $a->elixirCost - $b->elixirCost;
        });
    }
    if ($i == 2) { //por calidad
        usort($json->cards, function ($a, $b) {
            $valRarity = ['Common', 'Rare', 'Epic', 'Legendary', 'Champion'];
            return array_search($a->rarity, $valRarity) - array_search($b->rarity, $valRarity);
        });
    }
    if ($i == 3) { //por evolucion
        usort($json->cards, function ($a, $b) {
            $valEvo = ['si', 'no'];
            return array_search($a->evolution ? 'si' : 'no', $valEvo) - array_search($b->evolution ? 'si' : 'no', $valEvo);
        });
    }
    //por nivel(proximo)
    ?>
    <br>
    <hr>
    <br>
    <div id="div_tower-card_containers">
        <?php foreach ($json->towerCards as $key => $value) {
            $value->{'iconUrls'} = $jsonMedia->{$value->name}->iconUrls; ?>
            <div class="cs-card-space cs-card-space--medium" data-id="div_card_<?php echo $value->name; ?>">
                <div class="cs-card card--is-interactive" data-name="<?php echo $value->name; ?>"
                    data-json='<?php echo json_encode($value); ?>' data-type="tower" data-id="<?php echo $value->id; ?>"
                    data-inmazo="no">
                    <img class="cs-card__image" src="<?php echo $jsonMedia->{$value->name}->iconUrls->medium ?>"
                        title="<?php echo $value->name; ?>" alt="<?php echo $value->name; ?>">
                    <span class="cs-card__name"><?php echo $value->name; ?></span>

                    <div class="cs-card__options" id="cs-card__options" style="display: none;">
                        <button class="cs-card__info">Info</button><br>
                        <button class="cs-card__use-remove">Usar</button><br>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div><br>
    <hr>
    <br>
    <div id="div_card_containers">
        <?php foreach ($json->cards as $card) {
            $card->{'iconUrls'} = $jsonMedia->{$card->name}->iconUrls;
            $name = $card->name; ?>
            <div class="cs-card-space cs-card-space--medium" data-id="div_card_<?php echo $name; ?>">
                <div class="cs-card card--is-interactive" data-name="<?php echo $name; ?>"
                    data-json='<?php echo json_encode($card); ?>' data-type="card" data-id="<?php echo $card->id; ?>"
                    data-inmazo="no">
                    <img class="cs-card__image" src="<?php echo $jsonMedia->{$card->name}->iconUrls->medium; ?>"
                        title="<?php echo $name; ?>" alt="<?php echo $name; ?>">
                    <span class="cs-card__name"><?php echo $name; ?></span>
                    <span class="cs-card__elixir"><?php echo $card->elixirCost; ?></span>
                    <?php echo $card->evolution ? '<span class="cs-card__tag-evo">Evo</span>' : null ?>

                    <div class="cs-card__options" id="cs-card__options" style="display: none;">
                        <button class="cs-card__info">Info</button><br>
                        <button class="cs-card__use-remove">Usar</button><br>
                    </div>
                </div>
            </div>

        <?php } ?>
    </div>
    <?php $res['html' . ($i + 1)] = ob_get_clean();
}
