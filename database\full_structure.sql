CREATE DATABASE IF NOT EXISTS `clash_strategic`;

USE `clash_strategic`;

SET
    GLOBAL time_zone = '+00:00';

CREATE TABLE
    `users` (
        `id_Usu` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `<PERSON>eri<PERSON>do` TINYINT (1) DEFAULT 0,
        `AuthProvider` ENUM ('sistem', 'google') DEFAULT 'sistem' NOT NULL,
        `authId` VARCHAR(255) UNIQUE NOT NULL,
        `Tag` VARCHAR(20) NULL,
        `Avatar` VARCHAR(50) DEFAULT 'defect.webp',
        `Banner` VARCHAR(50) DEFAULT 'defect.webp',
        `Usuario` VARCHAR(15) UNIQUE NOT NULL,
        `Nombre` VARCHAR(15) NOT NULL,
        `Correo` VARCHAR(250) UNIQUE DEFAULT NULL,
        `Contrasena` VARCHAR(250),
        `Informacion` VARCHAR(350) DEFAULT NULL,
        `Fecha<PERSON>ac` date DEFAULT NULL,
        `Genero` VARCHAR(10) DEFAULT NULL,
        `Estado` VARCHAR(10) DEFAULT 'Activo',
        `PassStrategic` TINYINT (1) DEFAULT 0,
        `PasStrIni` DATETIME NULL,
        `Coins` INT DEFAULT 1000 not null,
        `Gems` INT DEFAULT 100 not null,
        `Mazos` VARCHAR(2000) NULL,
        `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `publications` (
        `id_Pub` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsPu` INT NOT NULL,
        `Contenido` VARCHAR(2000) NULL,
        `Fecha` DATETIME NOT NULL,
        `Archivo` VARCHAR(1500) NULL,
        `Comentarios` INT DEFAULT '0',
        `Reaccion1` INT DEFAULT '0',
        `Reaccion2` INT DEFAULT '0',
        `Reaccion3` INT DEFAULT '0',
        `Reaccion4` INT DEFAULT '0',
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Pub_Usu` FOREIGN KEY (`id_UsPu`) REFERENCES `users` (`id_Usu`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `chat` (
        `id_Cha` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsCh` INT NOT NULL,
        `id_MsgRep` INT DEFAULT NULL,
        `Mensaje` VARCHAR(1500) NOT NULL,
        `Fecha` DATETIME NOT NULL,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Cha_Usu` FOREIGN KEY (`id_UsCh`) REFERENCES `users` (`id_Usu`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `comments` (
        `id_Com` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsCo` INT NOT NULL,
        `id_PuCo` INT NOT NULL,
        `id_MsgRep` INT DEFAULT NULL,
        `Comentario` VARCHAR(1500),
        `Fecha` DATETIME NOT NULL,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Com_Usu` FOREIGN KEY (`id_UsCo`) REFERENCES `users` (`id_Usu`),
        CONSTRAINT `fk_Com_Pub` FOREIGN KEY (`id_PuCo`) REFERENCES `publications` (`id_Pub`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `reactions` (
        `id_Rea` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsRe` INT NOT NULL,
        `id_PuRe` INT NOT NULL,
        `Reaccion` INT DEFAULT '0',
        `Fecha` DATETIME NOT NULL,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Rea_Usu` FOREIGN KEY (`id_UsRe`) REFERENCES `users` (`id_Usu`),
        CONSTRAINT `fk_Rea_Pub` FOREIGN KEY (`id_PuRe`) REFERENCES `publications` (`id_Pub`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `sessions` (
        `id_Ses` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsSe` INT NOT NULL,
        `Session` VARCHAR(15) DEFAULT 'Activa',
        `IP` VARCHAR(50),
        `Token` VARCHAR(32) NOT NULL,
        `UserAgent` VARCHAR(255) NUll,
        `Fec_ini` DATETIME NOT NULL,
        `Fec_sal` DATETIME NUll,
        CONSTRAINT `fk_Ses_Usu` FOREIGN KEY (`id_UsSe`) REFERENCES `users` (`id_Usu`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `surveys` (
        `id_Enc` INT AUTO_INCREMENT PRIMARY KEY,
        `id_UsEn` INT NOT NULL,
        `Fecha` DATETIME NOT NULL,
        `Fecha_exp` DATETIME NOT NULL,
        `Pregunta` VARCHAR(500) NULL,
        `Imgpreg` VARCHAR(20) NULL,
        `Opcion1` VARCHAR(500) NULL,
        `Imagen1` VARCHAR(20) NULL,
        `Votos1` INT default 0,
        `Opcion2` VARCHAR(500) NULL,
        `Imagen2` VARCHAR(20) NULL,
        `Votos2` INT default 0,
        `Opcion3` VARCHAR(500) NULL,
        `Imagen3` VARCHAR(20) NULL,
        `Votos3` INT default 0,
        `Opcion4` VARCHAR(500) NULL,
        `Imagen4` VARCHAR(20) NULL,
        `Votos4` INT default 0,
        `Opcion5` VARCHAR(500) NULL,
        `Imagen5` VARCHAR(20) NULL,
        `Votos5` INT default 0,
        `Opcion6` VARCHAR(500) NULL,
        `Imagen6` VARCHAR(20) NULL,
        `Votos6` INT default 0,
        `Opcion7` VARCHAR(500) NULL,
        `Imagen7` VARCHAR(20) NULL,
        `Votos7` INT default 0,
        `Opcion8` VARCHAR(500) NULL,
        `Imagen8` VARCHAR(20) NULL,
        `Votos8` INT default 0,
        `Opcion9` VARCHAR(500) NULL,
        `Imagen9` VARCHAR(20) NULL,
        `Votos9` INT default 0,
        `Opcion10` VARCHAR(500) NULL,
        `Imagen10` VARCHAR(20) NULL,
        `Votos10` INT default 0,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Enc_Usu` FOREIGN KEY (`id_UsEn`) REFERENCES `users` (`id_Usu`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `votes` (
        `id_Vot` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsVo` INT NOT NULL,
        `id_EnVo` INT NULL,
        `Voto` INT DEFAULT 0,
        `Opcion` INT null,
        `Fecha` DATETIME NOT NULL,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Vot_Usu` FOREIGN KEY (`id_UsVo`) REFERENCES `users` (`id_Usu`),
        CONSTRAINT `fk_Vot_Enc` FOREIGN KEY (`id_EnVo`) REFERENCES `surveys` (`id_Enc`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    products (
        product_id VARCHAR(255) PRIMARY KEY,
        name ENUM ("gems", "coins", "deckanalyzer", "deckbuilder") NOT NULL,
        category ENUM ("CURRENCY", "TOOL") NOT NULL,
        description TEXT,
        amount INT NOT NULL,
        currency_price ENUM ("USD", "GEM", "COIN", "FREE") NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        state ENUM ("ACTIVE", "INACTIVE") DEFAULT "ACTIVE",
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `payments` (
        `id_Pay` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsPa` INT NOT NULL,
        `id_SePa` INT NOT NULL,
        `product_id` VARCHAR(255) DEFAULT 'wildcard' NOT NULL,
        `Purchase` VARCHAR(50) NOT NULL,
        `price` DECIMAL(10, 2) NOT NULL,
        `currency` ENUM ('USD') NOT NULL,
        `quantity` INT NOT NULL,
        `data` JSON NOT NULL,
        `status` ENUM ('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed' NOT NULL,
        `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Pay_Usu` FOREIGN KEY (`id_UsPa`) REFERENCES `users` (`id_Usu`),
        CONSTRAINT `fk_Pay_Ses` FOREIGN KEY (`id_SePa`) REFERENCES `sessions` (`id_Ses`),
        CONSTRAINT `fk_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `virtual_payments` (
        `id_Pur` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsPu` INT NOT NULL,
        `product_id` VARCHAR(255) DEFAULT 'wildcard' NOT NULL,
        `purchase` VARCHAR(15) NOT NULL,
        `price` DECIMAL(10, 2) NOT NULL,
        `currency` ENUM ('free', 'coin', 'gem') NOT NULL,
        `quantity` INT NOT NULL,
        `status` ENUM ('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed' NOT NULL,
        `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Pur_Usu` FOREIGN KEY (`id_UsPu`) REFERENCES `users` (`id_Usu`),
        CONSTRAINT `fk_virtual_payments_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `notifications` (
        `id_Not` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsNo` INT NOT NULL,
        `Title` VARCHAR(50) NOT NULL,
        `Body` VARCHAR(500) NOT NULL,
        `Icon` VARCHAR(50) NOT NULL,
        `Visto` TINYINT (1) default 0,
        `Fecha` DATETIME NOT NULL,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Not_Usu` FOREIGN KEY (`id_UsNo`) REFERENCES `users` (`id_Usu`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `strategies` (
        `id_Str` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_UsSt` INT NOT NULL,
        `Estado` VARCHAR(50) DEFAULT 'Activo' NOT NULL,
        `Visibilidad` VARCHAR(50) DEFAULT 'Publico' NOT NULL,
        `Nombre` VARCHAR(100) NOT NULL,
        `Descripcion` TEXT NOT NULL,
        `PrecioGema` INT DEFAULT 0 NOT NULL,
        `Condiciones` JSON DEFAULT NULL,
        `Creado` DATETIME NOT NULL,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Str_Usu` FOREIGN KEY (`id_UsSt`) REFERENCES `users` (`id_Usu`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

CREATE TABLE
    `conditions` (
        `id_Con` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
        `id_StCo` INT NOT NULL,
        `Condicion` JSON NOT NULL,
        `Puntos` FLOAT NOT NULL,
        `Mensaje` VARCHAR(250) NOT NULL,
        `Creado` DATETIME NOT NULL,
        `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT `fk_Con_Str` FOREIGN KEY (`id_StCo`) REFERENCES `strategies` (`id_Str`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;