CREATE TABLE
  `comments` (
    `id_Com` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsCo` INT NOT NULL,
    `id_PuCo` INT NOT NULL,
    `id_MsgRep` INT DEFAULT NULL,
    `Comentario` VARCHAR(1500),
    `<PERSON><PERSON>` DATETIME NOT NULL,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Com_Usu` FOREIGN KEY (`id_UsCo`) REFERENCES `users` (`id_Usu`),
    CONSTRAINT `fk_Com_Pub` FOREIGN KEY (`id_PuCo`) REFERENCES `publications` (`id_Pub`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;