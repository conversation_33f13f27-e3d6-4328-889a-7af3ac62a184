<?php

namespace App\Models;

use App\Services\{Database};
use App\Config\Config;
use App\Services\ClashRoyaleApi;

class User
{
    private $id;
    public $Name;
    public $User;
    public $Email;
    public $Password;
    public $Birthdate;
    public $Decks;
    public $Gems;
    public $Coins;
    public $Verify;
    public $TypeAcount;
    public $Tag;
    public $Avatar;
    public $Banner;
    public $Information;
    public $Gender;
    public $RegistrationDate;
    public $State;
    private $Database;
    public $Session;

    public function __construct(\PDO $connectionDB, int $id)
    {
        $this->Database = new Database($connectionDB);
        $result = $this->Database->query("SELECT * FROM users WHERE id_Usu = ?", [$id]);

        if (count($result) == 1) {
            $this->id = $result[0]['id_Usu'];
            $this->Name = $result[0]['Nombre'];
            $this->User = $result[0]['Usuario'];
            $this->Email = $result[0]['Correo'];
            //$this->Password = $result[0]['Contraseña'];
            $this->Birthdate = $result[0]['Fecha_Nac'] ?? 'Null';
            $this->Decks = $result[0]['Mazos'];
            $this->Gems = $result[0]['Gems'];
            $this->Coins = $result[0]['Coins'];
            $this->Verify = $result[0]['Verificado'];
            $this->TypeAcount = $result[0]['AuthProvider'];
            $this->Tag = $result[0]['Tag'] ?? 'Null';
            $this->Avatar = $result[0]['Avatar'];
            $this->Banner = $result[0]['Banner'];
            $this->Information = $result[0]['Informacion'];
            $this->Gender = $result[0]['Genero'] ?? 'Null';
            $this->RegistrationDate = $result[0]['created_at'];
            $this->State = $result[0]['Estado'];
            $userSession = $this->Database->query("SELECT * FROM sessions WHERE id_UsSe = ? AND Session = ?", [$id, 'Activa']);
            if (empty($userSession[0])) {
                throw new \Exception('No se encontró la sesión del usuario.');
            }
            $this->Session = $userSession[0];
        } else {
            throw new \Exception("No se encontró el usuario con el ID especificado.");
        }
    }

    public function register()
    {
        $db = $this->Database;
        $data = [
            'TypeAcount' => $this->TypeAcount,
            'Usuario' => $this->User,
            'Nombre' => $this->Name,
            'Correo' => $this->Email,
            'Contraseña' => $this->Password,
            'Fecha_Reg' => gmdate('Y-m-d H:i:s'),
        ];

        $db->insert('users', $data);

        $sql = "SELECT * FROM users WHERE Usuario = :user";
        $result = $db->query($sql, ['user' => $this->User]);

        if (count($result) === 1) {
            return true;
        }

        return false;
    }

    public function validate()
    {
        $errors = [];

        if (empty($this->Name)) {
            $errors[] = 'El nombre no puede estar vacío.';
        } elseif (strlen($this->Name) < 3 || strlen($this->Name) > 50) {
            $errors[] = 'El nombre debe tener entre 3 y 50 caracteres.';
        }

        if (empty($this->User)) {
            $errors[] = 'El nombre de usuario no puede estar vacío.';
        } elseif (strlen($this->User) < 3 || strlen($this->User) > 20) {
            $errors[] = 'El nombre de usuario debe tener entre 3 y 20 caracteres.';
        } elseif (!preg_match('/^[a-zA-Z0-9]+$/', $this->User)) {
            $errors[] = 'El nombre de usuario solo puede contener letras y números.';
        }

        if (!filter_var($this->Email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'El correo electrónico no es válido.';
        }

        if (strlen($this->Password) < 8) {
            $errors[] = 'La contraseña debe tener al menos 8 caracteres.';
        } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $this->Password)) {
            $errors[] = 'La contraseña debe contener al menos una letra mayúscula, una letra minúscula y un número.';
        }

        if (!strtotime($this->Birthdate)) {
            $errors[] = 'La fecha de nacimiento no es válida.';
        } else {
            $birthdate = new \DateTime($this->Birthdate);
            $today = new \DateTime();
            $age = $today->diff($birthdate)->y;
            if ($age < 18) {
                $errors[] = 'Debes ser mayor de 18 años para registrarte.';
            }
        }

        return $errors;
    }

    public function update(array $data)
    {
        return $this->Database->update('users', $data, ['id_Usu' => $this->id, 'Usuario' => $this->User]);
    }

    public function disableById($id)
    {
        $db = $this->Database;
        $db->update('users', ['Estado' => 'Inactivo'], "id_Usu = '{$id}'");
    }

    public static function setUserSession($type, $usarr, $connectionDB = null)
    {
        session_regenerate_id(true);
        switch ($type) {
            case 'google': //seesion con cuenta de google
                if (!$connectionDB) {
                    throw new \Exception('No se proporcionó una conexión a la base de datos.');
                }
                $Database = new Database($connectionDB);
                $infSes = $Database->query("SELECT * FROM sessions WHERE id_UsSe = '{$usarr['id_Usu']}' AND Session = 'Activa'");
                $rowinfSes = $infSes[0];
                break;
            case 'invitado': //session sin registro
                $rowinfSes = ['id_Ses' => null, 'Session' => 'Activa'];
                break;
        }

        $_SESSION['IP'] = gethostbyname($_SERVER['REMOTE_ADDR']); //obtener el ip y convertirlo en ipv4 para insertarlo en la session
        //isset($_COOKIE['Timezone']) && validateTimezone($_COOKIE['Timezone']) ? ($_SESSION['Timezone'] = $_COOKIE['Timezone']) : ($_SESSION['Timezone'] = 'UTC'); //sanitiza e inserta la timezone
        //$_SESSION['Device'] = getDeviceType();
        $_SESSION['UserAgent'] = $_SERVER['HTTP_USER_AGENT'];

        $_SESSION['id'] = $usarr['id_Usu'];
        $_SESSION['created_at'] = $usarr['created_at'];
        $_SESSION['Tag'] = $usarr['Tag'];
        $_SESSION['Usuario'] = $usarr['Usuario'];
        $_SESSION['Avatar'] = $usarr['Avatar'];
        $_SESSION['Banner'] = $usarr['Banner'];
        $_SESSION['Nombre'] = $usarr['Nombre'];
        $_SESSION['Correo'] = $usarr['Correo'];
        $_SESSION['FechaNac'] = $usarr['FechaNac'];
        $_SESSION['Verificado'] = $usarr['Verificado'];
        $_SESSION['Coins'] = $usarr['Coins'];
        $_SESSION['Gems'] = $usarr['Gems'];
        $_SESSION['Mazos'] = $usarr['Mazos'];
        $_SESSION['State'] = $usarr['Estado'];
        $_SESSION['TypeAcount'] = $type; //tipo de cuenta del usuario
        $_SESSION['Free'] = ['Gems' => false, 'Coins' => false];
        //$_SESSION['TypeAcount'] != 'invitado' && setObjectsFree($_SESSION['id']); //establece al iniciar session los objetos gratis reclamados de la shop

        $_SESSION['id_Ses'] = $rowinfSes['id_Ses'];
        $_SESSION['SessionState'] = $rowinfSes['Session']; //estado de la session, activa, eliminada etc

        $_SESSION['FechaSessionActual'] = gmdate('Y-m-d H:i:s');
        $_SESSION['EmotesShopDay'] = ['names' => [], 'srcs' => [], 'purchased' => [false, false, false], 'fech' => date('Y-m-d', strtotime('1970-01-01'))]; //se establese en esta fecha para que la primera ves se establesca los emotes con la hecha de hoy
        //setEmotesshop(); //no mover de esta posicion ya que los dos anteriores variables son importantes que esten definidas

        setcookie('CSDate', Config::getDateCS()['date'], 0, '/'); //establece la version actual del juego en la cookie para que se pueda comparar con la version actual del juego en el cliente y asi saber si se actualizo el juego o no
        setcookie('CSVersion', Config::getDateCS()['version'], 0, '/');
        setcookie('Mazos', $_SESSION['Mazos'], 0, '/');
        setcookie('TypeAcount', $_SESSION['TypeAcount'], 0, '/');
        //cookies de configuracion
        setcookie('sound_effects', 'true', 0, '/');
    }

    public static function insertSession($type, $connectionDB, $usarr = [], &$res = []) //inicia nueva session
    {
        $Database = new Database($connectionDB);
        switch ($type) {
            case 'google': //session para cuentas registradas con google
                $token = bin2hex(random_bytes(16));
                $ip = gethostbyname($_SERVER['REMOTE_ADDR']);

                $stmt = $Database->insert('sessions', [
                    'id_UsSe' => $usarr['id_Usu'],
                    'IP' => $ip,
                    'Token' => $token,
                    'UserAgent' => $_SERVER['HTTP_USER_AGENT'],
                    'Fec_ini' => gmdate('Y-m-d H:i:s')
                ]);

                $_SESSION['SessionToken'] = $token; //se asigna el nuevo token de session
                setcookie("SessionToken", $token, time() + (60 * 60 * 24 * 15), "/"); //establece la la session en la cookie(esto es seguro ya se hace una verificacion y se comparacon con la session)
                break;
            case 'invitado': //session para usuarios sin registro
                ($usarr = ['id_Usu' => null, 'Tag' => null, 'Usuario' => null, 'Avatar' => 'defect.jpg', 'Banner' => 'defect.webp', 'Nombre' => 'Invitado', 'Correo' => null, 'FechaNac' => null, 'Verificado' => null, 'Coins' => 0, 'Gems' => 0, 'TypeAcount' => 'invitado', 'Mazos' => '["", "", "", "", "", "", "", "", "", ""]']) && ($stmt = true);
                break;
        }
        if ($stmt) { //si no hubo ningun error al insertar la nueva session
            static::setUserSession($type, $usarr, $connectionDB);
            $res['login'] = true;
        } else {
            $res['res'] = '<p class="cs-color-IntenseOrange text-center">ha ocurrido un error</p>' && ($res['login'] = false);
        }
    }

    public function logout() //logout
    {
        $res = [];
        $loguotsession = $this->Database->update('sessions', ['Session' => 'Eliminada', 'Fec_sal' => gmdate('Y-m-d H:i:s')], ['id_UsSe' => $_SESSION['id'], 'Session' => 'Activa']);
        if ($loguotsession) { //si no ocurrio ningun error al eliminar las sessiones del usuario
            setcookie('SessionToken', ''); //elimina el token de session de la cookie
            session_unset();
            session_destroy();
            $res['cer_ses'] = true;
        } else { //si hubo un error en la consulta
            $res['cer_ses'] = false;
            $res['html'] = '<p class="cs-color-IntenseOrange text-center">No se cerro la session, ha ocurrido un error, intentelo mas tarde</p>';
        }
        return $res;
    }

    public static function googleAccess(\PDO $connectionDB) //acede con cuenta de google
    {
        $Database = new Database($connectionDB);
        $res = ["state" => "inprogres", "res" => "", "alerts" => []];
        if (!isset($_POST['credential'])) {
            $res['res'] = 'Error: No se recibió el token de autenticación.';
            $res['acount'] = false;
            return $res;
        }

        $token = $_POST['credential'];
        $ch = curl_init("https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=$token");

        /*         if ($_SERVER['SERVER_NAME'] === 'localhost') {
            curl_setopt($ch, CURLOPT_PROXY, '**********');
            curl_setopt($ch, CURLOPT_PROXYPORT, 7070);
        } */

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        curl_close($ch);

        if ($response === false) {
            $res['res'] = 'Error al conectar con el servicio de autenticación de Google.';
            $res['acount'] = false;
            return $res;
        }

        $data = json_decode($response, true);
        if (!is_array($data) || !isset($data['aud'])) {
            $res['res'] = 'Error: Respuesta de Google no válida o incompleta.';
            $res['acount'] = false;
        }

        if (!isset($data['email'])) {
            $res['res'] = 'Error: No se recibió el correo electrónico del usuario.';
            $res['acount'] = false;
            return $res;
        }

        if ($data['iss'] !== 'https://accounts.google.com') {
            $res['res'] = 'Error: El emisor del token no es válido.';
            $res['acount'] = false;
            return $res;
        }

        $expectedAud = '************-gqio8c2tjjsqhoo8dea7dmov2mft501o.apps.googleusercontent.com'; // Reemplaza con tu ID de cliente
        if ($data['aud'] !== $expectedAud) {
            $res['res'] = 'Error: La audiencia del token no es válida.';
            $res['acount'] = false;
            return $res;
        }

        $currentTime = time();
        if ($currentTime > $data['exp']) {
            $res['res'] = 'Error: El token ha expirado.';
            $res['acount'] = false;
            return $res;
        }

        if ($currentTime < $data['iat']) {
            $res['res'] = 'Error: El token fue emitido en el futuro.';
            $res['acount'] = false;
            return $res;
        }

        if ($data['email_verified'] !== 'true') {
            $res['res'] = 'Error: El correo electrónico de la cuenta no está verificado.';
            $res['acount'] = false;
            return $res;
        }

        $sqlAcount = $Database->query("SELECT * FROM users WHERE Correo = ?", [$data['email']]);
        $rowsesAcount = count($sqlAcount);

        if ($rowsesAcount == 0) { // Crear cuenta
            $resCreateAcount = static::createAcount($connectionDB, 'google', $data['name'] ?? 'undefined', $data['email'], null, null, ['authId' => $data['sub']]);
            if ($resCreateAcount['state'] === 'success') {
                $sesionNewUser = $Database->query("SELECT * FROM users WHERE Correo = ?", [$data['email']]);
                static::insertSession('google', $connectionDB, $sesionNewUser[0], $res);
                $res['acount'] = $res['new_user'] = true;
            } else {
                $res['res'] = $resCreateAcount['alerts'][0];
                $res['acount'] = false;
            }
        } elseif ($rowsesAcount == 1) { // Iniciar sesión
            $sesion = $sqlAcount[0];
            if ($data['sub'] !== $sesion['authId']) {
                $res['res'] = 'Error: El ID de autenticación no coincide.';
                $res['acount'] = false;
                return $res;
            }

            $Database->update('sessions', ['Session' => 'Eliminada', 'Fec_sal' => gmdate('Y-m-d H:i:s')], ['id_UsSe' => $sesion['id_Usu']]);
            static::insertSession('google', $connectionDB, $sesion, $res);
            $res['acount'] = true;
        }
        return $res;
    }

    public static function createAcount($connectionDB, $typeAcount, $nombre, $correo, $Contraseña, $RepContraseña, $opt = [])
    {
        $resCreateAcount = ['state' => 'inprogres', 'alerts' => [], 'usu' => []];
        $Database = new Database($connectionDB);

        if (!preg_match("/^[a-zA-Z0-9 ]*$/", $nombre)) { //solo mayusculas minusculas y numeros
            array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">Nombre no válido, no se permiten signos especiales: #$%&..!</span>');
            $resCreateAcount['state'] = 'error';
        }

        if (strlen($nombre) > 15) {
            array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡Caracteres superado en Nombre, la cantidad maxima es de 15, carcateres puestos ' . strlen($nombre) . ' !</span>');
            $resCreateAcount['state'] = 'error';
        }

        $nombre = trim($nombre);
        $nombre = static::cortarNombre($nombre, 15); //cortar a 15 caracteres
        $Mazos = '["", "", "", "", "", "", "", "", "", ""]';

        if (isset($_COOKIE['Mazos']) && $_COOKIE['Mazos'] != $Mazos) {
            $MazosArray = json_decode($_COOKIE['Mazos'], true);
            if (is_array($MazosArray) && count($MazosArray) == 10) {
                $div = $_COOKIE['Mazos'];
            }
        }

        if ($typeAcount == 'google') {
            $usuario = substr(md5(uniqid($correo, true)), 0, 15);
            $dataUser = [
                "AuthProvider" => 'google',
                "authId" => $opt['authId'],
                "Usuario" => $usuario,
                "Nombre" => $nombre,
                "Correo" => $correo,
                "Contrasena" => Null,
                "Mazos" => $Mazos
            ];
        } elseif ($typeAcount == 'sistem') {
            $usuario = strtolower(str_replace(' ', '', $nombre));
            if (strlen($Contraseña) < 8) {
                array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡La contraseña debe tener al menos 8 caracteres!</span>');
                $resCreateAcount['state'] = 'error';
            }

            if ($Contraseña !== $RepContraseña) {
                array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡Las contraseñas no coinciden!</span>');
                $resCreateAcount['state'] = 'error';
            }

            $resconusu = $Database->query("SELECT * FROM users WHERE Usuario = ?", [$usuario]);
            $resconusurows = count($resconusu);

            if ($resconusurows) {
                array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡El NOmbre de Usuario no esta Disponible, intenta con otra!</span>');
                $resCreateAcount['state'] = 'error';
            }

            $dataUser = [
                "TypeAcount" => $typeAcount,
                "Usuario" => $usuario,
                "Nombre" => $nombre,
                "Correo" => $correo,
                "Contrasena" => password_hash($Contraseña, PASSWORD_BCRYPT),
                "Fecha_Reg" => gmdate('Y-m-d H:i:s'),
                "Mazos" => '["", "", "", "", "", "", "", "", "", ""]'
            ];
        }

        $insertUser = $Database->insert('users', $dataUser);

        if ($insertUser) {
            $sqlusu = $Database->query("SELECT * FROM users WHERE Usuario = ?", [$usuario]);
            $resCreateAcount['usu'] = $sqlusu[0];
            //$this->Product->insertFreeEmotes($usu['id_Usu']);
            $resCreateAcount['state'] = 'success';
            array_push($resCreateAcount['alerts'], '<span class="cs-color-VibrantTurquoise text-center">¡Cuenta Creada Exitosamente!</span>');
        } else {
            array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡Ha ocurrido un Error!</span>');
            $resCreateAcount['state'] = 'error';
        }

        return $resCreateAcount;
    }

    public static function cortarNombre($nombre, $maxCaracteres)
    {
        if (strlen($nombre) <= $maxCaracteres) { // Si el nombre tiene una sola palabra, cortarlo si excede el máximo de caracteres
            return $nombre;
        }
        $palabras = explode(' ', $nombre);
        $nombreCortado = '';

        foreach ($palabras as $palabra) {
            if (strlen($nombreCortado . ' ' . $palabra) <= $maxCaracteres) { // Verificar si agregar la palabra excederá el límite de caracteres
                if ($nombreCortado === '') {
                    $nombreCortado = $palabra;
                } else {
                    $nombreCortado .= ' ' . $palabra;
                }
            } else {
                // Si es una sola palabra y excede el límite, cortarla
                if ($nombreCortado === '') {
                    return substr($palabra, 0, $maxCaracteres);
                }
                break;
            }
        }
        return $nombreCortado;
    }

    public static function validateTag(string $tag)
    {
        try {
            $typeKey = $_SERVER['DOCUMENT_ROOT'] == 'localhost' ? 'local' : 'great-site';
            $apiKey = Config::getCrApiKey($typeKey);
            $ClashRoyaleApi = new ClashRoyaleApi($apiKey);
            $dataProfile = $ClashRoyaleApi->getPlayerData($tag, ['profile']);
            return $dataProfile;
        } catch (\Exception $e) {
            throw new \Exception("Error al validar el tag -> " . $e->getMessage());
        }
    }

    public function saveDeck($nmazo, $mazo, &$res)
    {
        $resmaz = $this->Database->query("SELECT * FROM users WHERE id_Usu = ?", [$_SESSION['id']]);

        $Mazos = json_decode($resmaz[0]['Mazos'], true); // Todos los mazos del usuario de la bd
        $nmazo = $nmazo - 1;
        $mazoRecibido = $mazo;

        // Verificar si el mazo recibido es igual al mazo existente en la posición
        if ($Mazos[$nmazo] == $mazoRecibido) {
            $res['state'] = 'info';
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">El mazo ya esta guardado</span>';
        } else {
            // Proceder a guardar el mazo
            $Mazos[$nmazo] = $mazoRecibido; // mazo enviado desde el cliente insertado en el array $Mazos en la posición nmazo
            $mazoString = json_encode($Mazos); // convierte el array en string para insertarlo en la bd

            $insmaz = $this->Database->update('users', ["Mazos" => $mazoString], ["id_Usu" => $_SESSION['id']]);

            if ($insmaz) {
                $res['state'] = 'success';
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Mazo Guardado</span>';
            } else {
                $res['state'] = 'error';
                $res['res'] = '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error</span>';
            }
            $res['Mazos'] = $mazoString;
        }
    }
}
