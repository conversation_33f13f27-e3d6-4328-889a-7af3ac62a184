<img id="img_model_card" class="cs-card cs-card--large cs-card--model-fixed"
    src="<?= $data['jsonMedia']->{$data['card']->name}->iconUrls->medium ?? './Frontend/static/media/styles/logo/logo_clash_strategic.webp' ?>"
    alt="model">
<div id="div_barrasup_perfil" class="div_barrasup_perfil">
    <h3><?= $data['card']->name ?? 'Nombre Desconocido' ?></h3>
    <button id="btn_x-card" class="btn_x_perfil">X</button>
</div><br><br><br>

<div class="text-center m-a">
    <!--                 <div id="div_parent_tricks">
                        <?php /* $tricks = $data['jsonMedia']->{$data['card']->name}->tricks ?? []; */ ?>
                        <?php /* if (!empty($tricks)) { */ ?>
                            <div class="div_tricks" id="div_tricks">
                                <?php /* foreach ($tricks as $alltricks) { */ ?>
                                    <div class="div_tvd">
                                        <p><?php /* echo $alltricks->title; */ ?></p>
                                        <video poster="./Frontend/static/media/styles/miniatura_tricks.webp" class="video_tricks" src="<?php /* echo $alltricks->video; */ ?>" type="video/mp4" loop autoplay muted controls></video>
                                        <p><?php /* echo $alltricks->Description; */ ?></p>
                                    </div>
                                <?php /* } */ ?>
                            </div>
                        <?php /* } else { */ ?>
                            <p>Todavia no Hay trucos para esta carta, la implementaremos pronto.</p>
                            <p>"¿Tienes trucos?, Comparte el video y recibe 10 gemas por cada truco, mas inf <a class="cs-link cs-link--default" target="_blank" href="https://clashstrategic.notion.site/Comparte-Estrategias-y-Trucos-de-Clash-Royale-1402fc31dff94f0ebcc15f4d0ad56515?pvs=4">aqui</a>"</p>
                        <?php /* } */ ?>
                    </div> -->

    <?php
    // Definición de todas las estadísticas básicas
    $basicStats = [
        [
            'label' => 'Rareza',
            'icon' => './Frontend/static/media/styles/icons/info-circle.svg',
            'value' => $data['card']->rarity ?? 'Común',
            'alt' => 'rareza'
        ],
        [
            'label' => 'Costo de Elixir',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/icon_gota_elixir.webp',
            'value' => $data['card']->elixirCost ?? 0,
            'alt' => 'elixir'
        ],
        [
            'label' => 'Unidades',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/count.webp',
            'value' => $data['card']->units ?? 1,
            'alt' => 'unidades'
        ],
        [
            'label' => 'Evolución',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/evolution.webp',
            'value' => ($data['card']->evolution ?? false) ? 'Si' : 'No',
            'alt' => 'evo'
        ],
        [
            'label' => 'Tipo',
            'icon' => './Frontend/static/media/styles/icons/info-circle.svg',
            'value' => $data['card']->type ?? 'Tropa',
            'alt' => 'tipo'
        ],
        [
            'label' => isset($data['card']->range) ? 'Alcance' : (isset($data['card']->radius) ? 'Radio' : 'Alcance/Radio'),
            'icon' => isset($data['card']->radius) ?
                './Frontend/static/media/styles/icons/card_stat_inf/radius.webp' :
                './Frontend/static/media/styles/icons/card_stat_inf/range.webp',
            'value' => isset($data['card']->range) ? $data['card']->range :
                (isset($data['card']->radius) ? $data['card']->radius : 'N/A'),
            'alt' => isset($data['card']->radius) ? 'radius' : 'range'
        ],
        [
            'label' => 'Velocidad',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/speed.webp',
            'value' => $data['card']->speed ?? 'N/A',
            'alt' => 'speed'
        ],
        [
            'label' => 'Vlc. de Ataque',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/hit-speed.webp',
            'value' => $data['card']->hitspeed ?? 'N/A',
            'alt' => 'hit-speed'
        ]
    ];

    // Estadísticas de niveles (11 y 15)
    $levelStats = [
        [
            'label' => 'Vida',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/hitpoints.webp',
            'property' => 'hitpoints',
            'alt' => 'hitpoints'
        ],
        [
            'label' => 'Daño',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/damage.webp',
            'property' => 'damage',
            'alt' => 'damage'
        ],
        [
            'label' => 'DPS',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/damagepersec.webp',
            'property' => 'dps',
            'alt' => 'damagepersec'
        ],
        [
            'label' => 'Daño Mortal',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/deathdamage.webp',
            'property' => 'FatalDamage',
            'alt' => 'deathdamage',
            'conditional' => true
        ],
        [
            'label' => 'Daño a Torre',
            'icon' => './Frontend/static/media/styles/icons/card_stat_inf/crowntowerdamage.webp',
            'property' => 'TowerDamage',
            'alt' => 'crowntowerdamage',
            'conditional' => true
        ]
    ];
    ?>

    <table class="cs-table cs-table--light">
        <caption>Estadisticas</caption>
        <?php
        // Renderizar estadísticas básicas
        for ($i = 0; $i < count($basicStats); $i += 2):
            ?>
            <tr>
                <?php for ($j = 0; $j < 2 && ($i + $j) < count($basicStats); $j++):
                    $stat = $basicStats[$i + $j];
                    ?>
                    <td>
                        <div class="cs-media">
                            <div class="cs-media__figure">
                                <img class="cs-icon cs-icon--large" src="<?= $stat['icon'] ?>" alt="<?= $stat['alt'] ?>">
                            </div>
                            <div class="cs-media__body">
                                <span class="cs-color-DeepBlue"><?= $stat['label'] . ": " ?></span><br>
                                <span class="cs-color-DeepBlue"><?= $stat['value'] ?></span>
                            </div>
                        </div>
                    </td>
                <?php endfor; ?>
            </tr>
        <?php endfor; ?>

        <?php
        // Renderizar estadísticas de niveles
        foreach ($levelStats as $stat):
            if (isset($stat['conditional']) && $stat['conditional'] && !isset($data['card']->{$stat['property']})) {
                continue;
            }
            ?>
            <tr>
                <td>
                    <div class="cs-media">
                        <div class="cs-media__figure">
                            <img class="cs-icon cs-icon--large" src="<?= $stat['icon'] ?>" alt="<?= $stat['alt'] ?>">
                        </div>
                        <div class="cs-media__body">
                            <span class="cs-color-DeepBlue"><?= $stat['label'] ?> Nvl11: </span><br>
                            <span
                                class="cs-color-DeepBlue"><?= $data['card']->{$stat['property']}->level11 ?? 'N/A' ?></span>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="cs-media">
                        <div class="cs-media__figure">
                            <img class="cs-icon cs-icon--large" src="<?= $stat['icon'] ?>" alt="<?= $stat['alt'] ?>">
                        </div>
                        <div class="cs-media__body">
                            <span class="cs-color-DeepBlue"><?= $stat['label'] ?> Nvl15: </span><br>
                            <span
                                class="cs-color-DeepBlue"><?= $data['card']->{$stat['property']}->level15 ?? 'N/A' ?></span>
                        </div>
                    </div>
                </td>
            </tr>
        <?php endforeach; ?>
    </table>
    <div class="alert alert--info">
        <span
            class="alert__message"><?= $data['jsonMedia']->{$data['card']->name}->description ?? 'No hay descripción para esta carta' ?></span>
    </div><br><br><br>
    <!--                 <hr><br>

                    <h3>Wiki</h3>
                    <?php /* $wiki = $data['jsonMedia']->{$data['card']->name}->ResourcesUrls->wiki ?? '#'; */ ?>
                    <button id="btn_car_wiki" class="cs-btn cs-btn--medium cs-btn--primary cs-bg-color-1" data-url="<?php /* echo $wiki; */ ?>">Cargar/Ocultar Wiki</button>
                    <a class="card__details-url enlace" href="<?php /* echo $wiki; */ ?>" target="_blank"><?php /* echo $wiki; */ ?></a>
                    <div id="div_ifrm_wiki" style="display: none;">
                        <iframe id="ifrm_det_card" class="card__details-iframe" src=""></iframe>
                    </div> -->
</div><br><br><br><br><br>
<script>
    addSlick('vid', '.div_tricks');
</script>