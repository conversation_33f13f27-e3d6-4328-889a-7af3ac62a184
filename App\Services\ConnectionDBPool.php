<?php

namespace App\Services;

class ConnectionDBPool
{
    private $maxConnections = 1; //solo una coneccion por instancia
    private $availableConnections = [];
    private $inUseConnections = [];
    private $maxConnectionAttempts = 3; // Número máximo de intentos para obtener una conexión
    private $connectionAttemptDelay = 1; // Retraso en segundos entre intentos
    private $params;

    public function __construct(array $params)
    {
        if (empty($params['host']) || empty($params['dbname']) || empty($params['usuario']) || empty($params['contrasena'])) {
            throw new \InvalidArgumentException("Missing required database parameters.");
        }

        $this->params = $params;
    }

    private function createConnection(): ?\PDO
    {
        try {
            $options = [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_EMULATE_PREPARES => false,
                \PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES 'utf8mb4'" // Agrega codificación UTF-8
            ];

            $dsn = "mysql:host=" . $this->params['host'] . ";dbname=" . $this->params['dbname'] . ";charset=utf8mb4"; // Especifica UTF-8
            $connection = new \PDO($dsn, $this->params['usuario'], $this->params['contrasena'], $options);
            $connection->exec("SET time_zone = '+00:00'"); //conecciion en datetime UTC
            return $connection;
        } catch (\PDOException $e) {
            // Manejar errores de conexión, por ejemplo, verificar si es "too many connections"
            /*             if ($e->getCode() == 'HY000' || $e->getCode() == 2002 && strpos($e->getMessage(), "max_user_connections") !== false) {
                return null;
            } else {
                throw new \Exception("Database connection error: " . $e->getMessage());
            } */
            return null;
        }
    }

    public function getConnection(): ?\PDO
    {
        $connectionAttempts = 0;

        while ($connectionAttempts < $this->maxConnectionAttempts) {
            // Reutiliza una conexión disponible (si está activa)
            if (!empty($this->availableConnections)) {
                $connection = array_pop($this->availableConnections);
                if ($this->isConnectionAlive($connection)) {
                    $this->inUseConnections[] = $connection;
                    return $connection;
                } else {
                    // Elimina la conexión inválida
                    $connection = null;
                }
            }

            // Crea una nueva conexión si no se ha alcanzado el límite
            if (count($this->inUseConnections) < $this->maxConnections) {
                $connection = $this->createConnection();
                if ($connection !== null) {
                    $this->inUseConnections[] = $connection;
                    return $connection;
                }
            }

            $connectionAttempts++;
            if ($connectionAttempts < $this->maxConnectionAttempts) {
                sleep($this->connectionAttemptDelay);
                $this->connectionAttemptDelay *= 2;
            }
        }

        return null;
    }

    public function releaseConnection(\PDO $connection): void
    {
        $index = array_search($connection, $this->inUseConnections, true);
        if ($index !== false) {
            array_splice($this->inUseConnections, $index, 1);
            if ($this->isConnectionAlive($connection)) {
                $this->availableConnections[] = $connection;
            } else {
                $connection = null; // Descarta conexiones inválidas
            }
        }
    }

    private function isConnectionAlive(\PDO $connection): bool
    {
        try {
            $connection->query('SELECT 1');
            return true;
        } catch (\PDOException $e) {
            return false;
        }
    }

    public function closeAllConnections()
    {
        foreach ($this->availableConnections as $connection) {
            $connection = null;
        }
        $this->availableConnections = [];
        foreach ($this->inUseConnections as $connection) {
            $connection = null;
        }
        $this->inUseConnections = [];
    }

    public function __destruct()
    {
        $this->closeAllConnections();
    }
}
