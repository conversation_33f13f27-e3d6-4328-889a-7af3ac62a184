[{"name": "Common", "levelCount": 15, "relativeLevel": 0, "tournamentLevelIndex": 10, "donateCapacity": 1, "sortCapacity": 1, "donateReward": 5, "donateXp": 1, "overflowPrestige": 1, "goldConversionValue": 5, "eliteWildcardsConversionValue": 1, "maxLevelDonationCost": 5, "tradeCardAmount": 250, "chanceWeight": 1000, "upgradeExp": [4, 5, 6, 10, 25, 50, 100, 200, 400, 600, 800, 1600, 2000, 50000, 0], "upgradeMaterialCount": [2, 4, 10, 20, 50, 100, 200, 400, 800, 1000, 1500, 3000, 5000, 50000, 0], "originalUpgradeMaterialCount": [2, 4, 10, 20, 50, 100, 200, 400, 800, 1000, 2000, 5000, 0, 0, 0], "upgradeCost": [5, 20, 50, 150, 400, 1000, 2000, 4000, 8000, 15000, 35000, 75000, 100000, 0, 0], "powerLevelMultiplier": [110, 121, 133, 146, 160, 176, 193, 212, 233, 256, 281, 309, 339, 372, 409, 450, 495, 545, 600], "refundGems": 2, "tid": "TID_RARITY_COMMON", "castEffect": "SpellCasting", "sortOrder": 1, "red": [153], "green": [204], "blue": [255], "appearEffect": "openChest_get_common", "buySound": "sound_buy_card_common", "loopEffect": "openChest_loop_common", "cardTxtBgFrameIdx": 0, "cardGlowInstanceName": "", "spellSelectedSound": "", "spellAvailableSound": "", "rotateExportName": "card_rotate_common_short", "goldFrameExportName": "card_frame_gold", "iconSwf": "sc/ui.sc", "tokenSwf": "sc/ui_spells.sc", "tokenExportName": "token_common", "tokenFlyingIconSwf": "sc/ui.sc", "tokenFlyingIconExportName": "token_common", "tokenGoldValue": 250, "npcTraderRerollGemCost": 10, "canBeInChests": true, "ifChestChanceOverXpercentDropGuaranteed": null, "taskExpReward": [0, 25, 100, 150], "downgradeRarity": "", "downgradeMultiplier": 0, "maxAmountInChronosShopOffers": 100000, "overflowCardPerShard": 1000, "evoLevelColor": ["249-90-255", "255-255-255", "249-90-255", "255-255-255", "249-90-255"], "cardCappedLevelBgExportName": "card_item_level_cap", "cardCappedLevelBgFrameIdx": 0, "cardEvoCappedLevelBgExportName": "card_item_level_cap_evolution", "iconFile": "icon_btn_card_common", "supportPowerLevel": [108, 116, 125, 135, 145, 156, 168, 181, 199, 218, 239, 262, 288, 316]}, {"name": "Rare", "levelCount": 13, "relativeLevel": 2, "tournamentLevelIndex": 8, "donateCapacity": 10, "sortCapacity": 7, "donateReward": 50, "donateXp": 10, "overflowPrestige": 10, "goldConversionValue": 50, "eliteWildcardsConversionValue": 5, "maxLevelDonationCost": 25, "tradeCardAmount": 50, "chanceWeight": 400, "upgradeExp": [6, 10, 25, 50, 100, 200, 400, 600, 800, 1600, 2000, 50000, 0], "upgradeMaterialCount": [2, 4, 10, 20, 50, 100, 200, 400, 500, 750, 1250, 50000, 0], "originalUpgradeMaterialCount": [2, 4, 10, 20, 50, 100, 200, 400, 800, 1000, 0, 0, 0], "upgradeCost": [50, 150, 400, 1000, 2000, 4000, 8000, 15000, 35000, 75000, 100000, 0, 0], "powerLevelMultiplier": [110, 121, 133, 146, 160, 176, 193, 212, 233, 256, 281, 309, 339, 372, 409, 450, 495], "refundGems": 15, "tid": "TID_RARITY_RARE", "castEffect": "SpellCasting", "sortOrder": 2, "red": [255], "green": [204], "blue": [102], "appearEffect": "openChest_get_rare", "buySound": "sound_buy_card_rare", "loopEffect": "openChest_loop_rare", "cardTxtBgFrameIdx": 1, "cardGlowInstanceName": "card_glow_rare", "spellSelectedSound": "", "spellAvailableSound": "", "rotateExportName": "card_rotate_epic_short", "goldFrameExportName": "card_frame_gold", "iconSwf": "sc/ui.sc", "tokenSwf": "sc/ui_spells.sc", "tokenExportName": "token_rare", "tokenFlyingIconSwf": "sc/ui.sc", "tokenFlyingIconExportName": "token_rare", "tokenGoldValue": 500, "npcTraderRerollGemCost": 10, "canBeInChests": true, "ifChestChanceOverXpercentDropGuaranteed": null, "taskExpReward": [0, 50, 125, 175], "downgradeRarity": "Common", "downgradeMultiplier": 1, "maxAmountInChronosShopOffers": 10000, "overflowCardPerShard": 250, "evoLevelColor": ["249-90-255", "255-255-255", "249-90-255", "255-255-255", "249-90-255"], "cardCappedLevelBgExportName": "card_item_level_cap", "cardCappedLevelBgFrameIdx": 1, "cardEvoCappedLevelBgExportName": "card_item_level_cap_evolution", "iconFile": "icon_btn_card_rare", "supportPowerLevel": [108, 116, 125, 135, 145, 156, 168, 181, 199, 218, 239, 262, 288, 316]}, {"name": "Epic", "levelCount": 10, "relativeLevel": 5, "tournamentLevelIndex": 5, "donateCapacity": 10, "sortCapacity": 80, "donateReward": 500, "donateXp": 10, "overflowPrestige": 100, "goldConversionValue": 500, "eliteWildcardsConversionValue": 20, "maxLevelDonationCost": 100, "tradeCardAmount": 10, "chanceWeight": 40, "upgradeExp": [25, 100, 200, 400, 600, 800, 1600, 2000, 50000, 0], "upgradeMaterialCount": [2, 4, 10, 20, 40, 50, 100, 200, 50000, 0], "originalUpgradeMaterialCount": [2, 4, 10, 20, 50, 100, 200, 0, 0, 0], "upgradeCost": [400, 2000, 4000, 8000, 15000, 35000, 75000, 100000, 0, 0], "powerLevelMultiplier": [110, 121, 133, 146, 160, 176, 193, 212, 233, 256, 281, 309, 339, 372], "refundGems": 300, "tid": "TID_RARITY_EPIC", "castEffect": "SpellCasting", "sortOrder": 3, "red": [255], "green": [153], "blue": [255], "appearEffect": "openChest_get_epic", "buySound": "sound_buy_card_epic", "loopEffect": "openChest_loop_epic", "cardTxtBgFrameIdx": 2, "cardGlowInstanceName": "card_frame_glow_epic", "spellSelectedSound": "", "spellAvailableSound": "", "rotateExportName": "card_rotate_rare_short", "goldFrameExportName": "card_frame_gold", "iconSwf": "sc/ui.sc", "tokenSwf": "sc/ui_spells.sc", "tokenExportName": "token_epic", "tokenFlyingIconSwf": "sc/ui.sc", "tokenFlyingIconExportName": "token_epic", "tokenGoldValue": 1000, "npcTraderRerollGemCost": 10, "canBeInChests": true, "ifChestChanceOverXpercentDropGuaranteed": null, "taskExpReward": [0, 75, 125, 175], "downgradeRarity": "Rare", "downgradeMultiplier": 1, "maxAmountInChronosShopOffers": 2000, "overflowCardPerShard": 40, "evoLevelColor": ["249-90-255", "255-255-255", "249-90-255", "255-255-255", "249-90-255"], "cardCappedLevelBgExportName": "card_item_level_cap", "cardCappedLevelBgFrameIdx": 2, "cardEvoCappedLevelBgExportName": "card_item_level_cap_evolution", "iconFile": "icon_btn_card_epic", "supportPowerLevel": [108, 116, 125, 135, 145, 156, 168, 181, 199, 218, 239, 262, 288, 316]}, {"name": "Legendary", "levelCount": 7, "relativeLevel": 8, "tournamentLevelIndex": 2, "donateCapacity": 4000, "sortCapacity": 2000, "donateReward": 500, "donateXp": 25, "overflowPrestige": 1000, "goldConversionValue": 20000, "eliteWildcardsConversionValue": 1500, "maxLevelDonationCost": 7500, "tradeCardAmount": 1, "chanceWeight": 10, "upgradeExp": [250, 600, 800, 1600, 2000, 50000, 0], "upgradeMaterialCount": [2, 4, 6, 10, 20, 50000, 0], "originalUpgradeMaterialCount": [2, 4, 10, 20, 0, 0, 0], "upgradeCost": [5000, 15000, 35000, 75000, 100000, 0, 0], "powerLevelMultiplier": [110, 121, 133, 146, 160, 176, 193, 212, 233, 256, 281], "refundGems": 8000, "tid": "TID_RARITY_LEGENDARY", "castEffect": "SpellCasting", "sortOrder": 4, "red": [255, 255, 255, 153, 153], "green": [153, 153, 255, 255, 255], "blue": [255, 255, 153, 102, 102], "appearEffect": "openChest_get_legendary", "buySound": "sound_buy_card_legendary", "loopEffect": "openChest_loop_legendary", "cardTxtBgFrameIdx": 3, "cardGlowInstanceName": "card_frame_glow_legendary", "spellSelectedSound": "Select legendary spell", "spellAvailableSound": "Legendary spell available", "rotateExportName": "card_rotate_legendary_short", "goldFrameExportName": "legendary_card_frame_gold", "iconSwf": "sc/ui.sc", "tokenSwf": "sc/ui_spells.sc", "tokenExportName": "token_legendary", "tokenFlyingIconSwf": "sc/ui.sc", "tokenFlyingIconExportName": "token_legendary", "tokenGoldValue": 2000, "npcTraderRerollGemCost": 10, "canBeInChests": true, "ifChestChanceOverXpercentDropGuaranteed": 75, "taskExpReward": [0, 100, 150, 200], "downgradeRarity": "Epic", "downgradeMultiplier": 1, "maxAmountInChronosShopOffers": 200, "overflowCardPerShard": 2, "evoLevelColor": ["249-90-255", "255-255-255", "249-90-255", "255-255-255", "249-90-255"], "cardCappedLevelBgExportName": "card_item_level_cap_support_card", "cardCappedLevelBgFrameIdx": 3, "cardEvoCappedLevelBgExportName": "card_item_level_cap_evolution_legendary", "iconFile": "icon_btn_card_legendary", "supportPowerLevel": [108, 116, 125, 135, 145, 156, 168, 181, 199, 218, 239]}, {"name": "Champion", "levelCount": 5, "relativeLevel": 10, "tournamentLevelIndex": 0, "donateCapacity": 4000, "sortCapacity": 4000, "donateReward": 1000, "donateXp": 50, "overflowPrestige": 2000, "goldConversionValue": 40000, "eliteWildcardsConversionValue": 4000, "maxLevelDonationCost": 40000, "tradeCardAmount": 1, "chanceWeight": 1, "upgradeExp": [800, 1600, 2000, 50000, 0], "upgradeMaterialCount": [2, 8, 20, 50000, 0], "originalUpgradeMaterialCount": [0, 0, 0, 0, 0], "upgradeCost": [35000, 75000, 100000, 0, 0], "powerLevelMultiplier": [110, 121, 133, 146, 160, 176, 193, 212, 233], "refundGems": 8000, "tid": "TID_RARITY_CHAMPION", "castEffect": "SpellCasting", "sortOrder": 5, "red": [255, 255, 253], "green": [222, 217, 251], "blue": [0, 100, 222], "appearEffect": "openChest_get_champion", "buySound": "sound_buy_card_legendary", "loopEffect": "openChest_loop_champion", "cardTxtBgFrameIdx": 8, "cardGlowInstanceName": "card_frame_glow_champion", "spellSelectedSound": "", "spellAvailableSound": "", "rotateExportName": "card_rotate_legendary_short", "goldFrameExportName": "champion_card_frame_gold", "iconSwf": "sc/ui.sc", "tokenSwf": "sc/ui_spells.sc", "tokenExportName": "token_legendary", "tokenFlyingIconSwf": "sc/ui.sc", "tokenFlyingIconExportName": "token_legendary", "tokenGoldValue": 4000, "npcTraderRerollGemCost": 10, "canBeInChests": true, "ifChestChanceOverXpercentDropGuaranteed": 90, "taskExpReward": [0, 150, 175, 200], "downgradeRarity": "Legendary", "downgradeMultiplier": 1, "maxAmountInChronosShopOffers": 100, "overflowCardPerShard": 1, "evoLevelColor": ["249-90-255", "255-255-255", "249-90-255", "255-255-255", "249-90-255"], "cardCappedLevelBgExportName": "card_item_level_cap_support_card", "cardCappedLevelBgFrameIdx": 4, "cardEvoCappedLevelBgExportName": "card_item_level_cap_evolution_legendary", "iconFile": "icon_btn_card_champion", "supportPowerLevel": [108, 116, 125, 135, 145, 156, 168, 181, 199]}, {"name": "Experimental", "levelCount": 8, "relativeLevel": 8, "tournamentLevelIndex": 2, "donateCapacity": 4000, "sortCapacity": 2000, "donateReward": 50, "donateXp": 25, "overflowPrestige": 1000, "goldConversionValue": 20000, "eliteWildcardsConversionValue": 100, "maxLevelDonationCost": 20000, "tradeCardAmount": 1, "chanceWeight": 1, "upgradeExp": [250, 600, 800, 1600, 1600, 2400, 2400, 0], "upgradeMaterialCount": [2, 4, 10, 20, 20, 20, 50000, 0], "originalUpgradeMaterialCount": [0, 0, 0, 0, 0, 0, 0, 0], "upgradeCost": [5000, 20000, 50000, 100000, 100000, 100000, 0, 0], "powerLevelMultiplier": [110, 121, 133, 146, 160, 176, 193], "refundGems": 8000, "tid": "TID_RARITY_LEGENDARY", "castEffect": "SpellCasting", "sortOrder": 6, "red": [255, 255, 255, 153, 153], "green": [153, 153, 255, 255, 255], "blue": [255, 255, 153, 102, 102], "appearEffect": "openChest_get_legendary", "buySound": "sound_buy_card_legendary", "loopEffect": "openChest_loop_legendary", "cardTxtBgFrameIdx": 3, "cardGlowInstanceName": "card_frame_glow_legendary", "spellSelectedSound": "Select legendary spell", "spellAvailableSound": "Legendary spell available", "rotateExportName": "card_rotate_legendary_short", "goldFrameExportName": "card_frame_gold", "iconSwf": "sc/ui.sc", "tokenSwf": "sc/ui_spells.sc", "tokenExportName": "token_legendary", "tokenFlyingIconSwf": "sc/ui.sc", "tokenFlyingIconExportName": "token_legendary", "tokenGoldValue": 2000, "npcTraderRerollGemCost": 10, "canBeInChests": false, "ifChestChanceOverXpercentDropGuaranteed": null, "taskExpReward": [], "downgradeRarity": "", "downgradeMultiplier": null, "maxAmountInChronosShopOffers": 1, "overflowCardPerShard": 0, "evoLevelColor": ["249-90-255", "255-255-255", "249-90-255", "255-255-255", "249-90-255"], "cardCappedLevelBgExportName": "", "cardCappedLevelBgFrameIdx": null, "cardEvoCappedLevelBgExportName": "", "iconFile": "icon_btn_card_legendary", "supportPowerLevel": [108, 116, 125, 135, 145, 156, 168]}]