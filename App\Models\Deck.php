<?php

namespace App\Models;

use function PHPUnit\Framework\throwException;

class Deck
{
    public array $Cards;
    public array $CardsEvo;

    public function __construct(array $CardsNamesIds, int $level = 11, int $slotsEvolved = 0)
    {
        if (count($CardsNamesIds) !== 9)
            throw new \InvalidArgumentException('A deck must have exactly 9 cards.');

        if (count(array_unique($CardsNamesIds)) !== 9)
            throw new \InvalidArgumentException('All cards must have unique IDs: ' . implode(', ', $CardsNamesIds));

        if ($level < 1 || $level > 15)
            throw new \InvalidArgumentException('The level must be an integer between 1 and 15.');

        if (!in_array($slotsEvolved, [0, 1, 2], true))
            throw new \InvalidArgumentException("El parámetro \$anaEvo debe ser un entero (0, 1, o 2).");

        $Cards = array_map(function ($nameId) use ($level) {
            return new Card($nameId, $level, false);
        }, $CardsNamesIds);

        $CardsEvo = [];
        foreach ($Cards as $index => $Card) {
            if ($index == 2)
                break;

            if ($slotsEvolved && $Card->evolution && ($index == 0 || $index == 1)) {
                $CardsEvo[] = new Card($Card->name, $level, 2);
            }
        }

        $this->Cards = $Cards;
        $this->CardsEvo = $CardsEvo;
    }

    public function getAllStats(): array
    {
        return array_map(function ($card) {
            return $card->stats;
        }, $this->Cards);
    }

    public function getAllMedia(): array
    {
        return array_map(function ($card) {
            return $card->media;
        }, $this->Cards);
    }
}
