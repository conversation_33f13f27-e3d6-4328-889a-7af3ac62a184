CREATE TABLE
  `payments` (
    `id_Pay` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsPa` INT NOT NULL,
    `id_SePa` INT NOT NULL,
    `Purchase` VARCHAR(50) NOT NULL,
    `price` INT NOT NULL,
    `currency` ENUM ('USD') NOT NULL,
    `quantity` INT NOT NULL,
    `data` JSON NOT NULL,
    `status` ENUM ('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed' NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- REVISAR ESTAS FOREIGN KEYS CUIDADOSAMENTE:
    CONSTRAINT `fk_Pay_Usu` FOREIGN KEY (`id_UsPa`) REFERENCES `users` (`id_Usu`),
    CONSTRAINT `fk_Pay_Ses` FOREIGN KEY (`id_SePa`) REFERENCES `sessions` (`id_Ses`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;