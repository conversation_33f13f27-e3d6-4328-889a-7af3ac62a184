<?php

namespace App\Services;

use App\Controllers\{<PERSON>r<PERSON><PERSON><PERSON><PERSON>, DeckController, CardController, ProductController, SectionController, AboutUsController};
use App\Config\Config;
use App\Services\ConnectionDBPool;
use PDO;

class UserRequest
{
    private $postData;
    private $userType;
    private $sessionData;
    private $dbPool;
    private $dbConnection;

    // Define las acciones, sus controladores, métodos, si requieren BD y qué tipos de usuario pueden acceder
    private $actionMap = [
        // Acciones Invitado, Google, Sistem
        'session' => ['class' => UserController::class, 'method' => 'logout', 'db' => true, 'access' => ['google', 'sistem', 'invitado'], 'needsSessionId' => true],
        'credential' => ['class' => UserController::class, 'method' => 'googleAccess', 'db' => true, 'access' => ['google', 'sistem', 'invitado']],
        'analizarMazoBasico' => ['class' => DeckController::class, 'method' => 'analyze', 'db' => true, 'access' => ['google', 'sistem', 'invitado'], 'params' => ['namesCards', 'AnaEvo', 'analysisType' => 'basic']],
        'analizarMazoIntermedio' => ['class' => DeckController::class, 'method' => 'analyze', 'db' => true, 'access' => ['google', 'sistem', 'invitado'], 'params' => ['namesCards', 'AnaEvo', 'analysisType' => 'intermediate']],
        'getSettings' => ['class' => AboutUsController::class, 'method' => 'getSettings', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],
        'showCards' => ['class' => CardController::class, 'method' => 'showAll', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],
        'infcards' => ['class' => CardController::class, 'method' => 'infcards', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],
        'acercaDe' => ['class' => AboutUsController::class, 'method' => 'showAbout', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],
        'getSections' => ['class' => SectionController::class, 'method' => 'show', 'db' => false, 'access' => ['google', 'sistem', 'invitado'], 'params' => ['sectionName']], // Asumiendo que el valor de getSections es el nombre
        'verCSVersion' => ['class' => AboutUsController::class, 'method' => 'verCSVersion', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],
        'sobreNosotros' => ['class' => AboutUsController::class, 'method' => 'showAboutUs', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],
        'RegLog' => ['class' => AboutUsController::class, 'method' => 'RegLog', 'db' => false, 'access' => ['google', 'sistem', 'invitado'], 'params' => ['type']], // 'type' vendrá de $_POST['type']
        'PreCS' => ['class' => AboutUsController::class, 'method' => 'showPresentationCs', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],
        'termcond' => ['class' => AboutUsController::class, 'method' => 'termcond', 'db' => false, 'access' => ['google', 'sistem', 'invitado']],

        // Acciones Google, Sistem
        'showUserBasicData' => ['class' => UserController::class, 'method' => 'showBasicData', 'db' => true, 'access' => ['google', 'sistem'], 'needsSessionId' => true],
        'setTag' => ['class' => UserController::class, 'method' => 'setTag', 'db' => true, 'access' => ['google', 'sistem'], 'needsSessionId' => true, 'params' => ['Tag']],
        'analizarMazoAvanzado' => ['class' => DeckController::class, 'method' => 'analyze', 'db' => true, 'access' => ['google', 'sistem'], 'params' => ['namesCards', 'AnaEvo', 'analysisType' => 'advanced']],
        'guardarMazo' => ['class' => DeckController::class, 'method' => 'guardarMazo', 'db' => true, 'access' => ['google', 'sistem'], 'params' => ['mazo', 'nmazo']],
        'crearMazo' => ['class' => DeckController::class, 'method' => 'create', 'db' => true, 'access' => ['google', 'sistem']],
    ];

    /**
     * Constructor de UserRequest.
     *
     * @param array $postData Datos recibidos por POST.
     * @param string|null $userType Tipo de usuario ('invitado', 'google', 'sistem') o null si no está definido.
     * @param array $sessionData Datos de la sesión actual.
     */
    public function __construct(array $postData, ?string $userType, array $sessionData)
    {
        $this->postData = $postData;
        $this->userType = $userType ?? 'invitado';
        $this->sessionData = $sessionData;
        $this->dbPool = null;
        $this->dbConnection = null;
    }

    /**
     * Maneja la solicitud del usuario determinando la acción, verificando permisos,
     * conectando a la BD si es necesario, preparando parámetros y ejecutando
     * el método del controlador correspondiente.
     *
     * @return array Un array asociativo con el resultado de la operación,
     *               generalmente conteniendo 'state' ('success' o 'error'),
     *               'alerts' (mensajes para el usuario) y opcionalmente 'data'.
     */
    public function handleRequest(): array
    {
        $action = $this->determineAction();
        if (!$action) {
            return ["state" => "error", "alerts" => ["Acción no reconocida o no especificada."]];
        }

        if (!isset($this->actionMap[$action])) {
            return ["state" => "error", "alerts" => ["La acción '$action' no está definida."]];
        }

        $config = $this->actionMap[$action];

        // 1. Verificar Permisos
        if (!in_array($this->userType, $config['access'])) {
            $message = $this->userType === 'invitado'
                ? '¡Aún eres invitado! Regístrate o inicia sesión para acceder a esta función.'
                : 'No tienes permiso para realizar esta acción.';
            return ["state" => "error", "alerts" => [$message]];
        }

        try {
            // 2. Manejar Conexión a BD si es necesaria
            if ($config['db']) {
                $this->dbPool = new ConnectionDBPool(Config::getParamDB($_SERVER['SERVER_NAME']));
                $this->dbConnection = $this->dbPool->getConnection();
                if ($this->dbConnection === null) {
                    // No lanzar excepción aquí, devolver error JSON amigable
                    return ["state" => "error", "alerts" => ["Lo sentimos, no podemos conectarnos a la base de datos en este momento. Por favor, inténtalo de nuevo más tarde."]];
                }

                // Verificar sesión activa para usuarios 'google' si la acción requiere BD
                if ($this->userType === 'google' && isset($this->sessionData['id'])) {
                    // Instanciamos temporalmente solo para verificar sesión
                    $userSessionController = new UserController($this->dbConnection, $this->sessionData['id']);
                    if (!$userSessionController->isSessionActive()) {
                        // Considera si quieres hacer logout aquí o solo devolver error
                        // $userSessionController->logout(); // Podría causar problemas si se llama desde API
                        return ["state" => "error", "alerts" => ["Tu sesión ha expirado. Por favor, inicia sesión de nuevo."], "action" => "logout_redirect"]; // Acción especial para el frontend
                    }
                }
            }

            // 3. Preparar Parámetros
            $controllerParams = [];
            $methodParams = [];

            // Añadir conexión a BD a los parámetros del constructor si es necesaria
            if ($config['db']) {
                $controllerParams[] = $this->dbConnection;
            }
            // Añadir ID de sesión si es necesario para el constructor (como en UserController)
            if (!empty($config['needsSessionId']) && isset($this->sessionData['id'])) {
                $controllerParams[] = $this->sessionData['id'];
            }

            // Preparar parámetros para el método
            if (isset($config['params'])) {
                foreach ($config['params'] as $paramKey => $paramSource) {
                    if (is_string($paramSource) && isset($this->postData[$paramSource])) {
                        // Decodificar JSON si es necesario (ej: 'mazo', 'namesCards')
                        if (in_array($paramSource, ['mazo', 'namesCards'])) {
                            $decoded = json_decode($this->postData[$paramSource], true);
                            $methodParams[] = $decoded === null ? $this->postData[$paramSource] : $decoded; // Usar original si falla decodificación
                        } elseif ($paramSource === 'AnaEvo') {
                            $methodParams[] = intval($this->postData[$paramSource]); // Asegurar entero
                        } else {
                            $methodParams[] = $this->postData[$paramSource];
                        }
                    } elseif (is_string($paramKey) && !is_numeric($paramKey)) {
                        // Parámetro fijo definido en el mapa (ej: analysisType)
                        $methodParams[] = $paramSource;
                    } else {
                        // Parámetro requerido no encontrado en POST
                        return ["state" => "error", "alerts" => ["Falta el parámetro requerido: " . (is_string($paramSource) ? $paramSource : $paramKey)]];
                    }
                }
            }

            // 4. Ejecutar Acción
            $controllerClass = $config['class'];
            $method = $config['method'];

            $instance = new $controllerClass(...$controllerParams);
            $result = $instance->$method(...$methodParams);

            // 5. Devolver Respuesta Exitosa
            // Asumimos que los métodos devuelven los datos directamente o un array con 'state', 'alerts', 'data'
            if (is_array($result) && isset($result['state'])) {
                return $result; // El método ya formateó la respuesta
            } else {
                // Formato estándar si el método solo devolvió datos
                return ["state" => "success", "alerts" => ["Acción '$action' ejecutada correctamente."], "data" => $result];
            }

        } catch (\Exception $e) {
            // Loggear el error $e->getMessage(), $e->getFile(), $e->getLine(), $e->getTraceAsString()
            // ... (añadir lógica de logging similar a la del Router) ...
            return ["state" => "error", "alerts" => ["Error al procesar la acción '$action': " . $e->getMessage()]];
        } finally {
            // 6. Liberar Conexión a BD
            if ($this->dbConnection !== null && $this->dbPool !== null) {
                $this->dbPool->releaseConnection($this->dbConnection);
            }
        }
    }

    /**
     * Determina la acción a realizar basándose en las claves presentes en los datos POST
     * que coinciden con las claves definidas en $actionMap.
     * Da prioridad a una clave 'action' explícita si existe.
     * Maneja casos especiales como 'analizarMazo' que dependen de un 'type'.
     *
     * @return string|null El nombre de la acción determinada o null si no se puede determinar una acción única o válida.
     */
    private function determineAction(): ?string
    {
        $possibleActions = array_intersect_key($this->postData, $this->actionMap);
        if (count($possibleActions) === 1) {
            return key($possibleActions);
        }
        // Si hay una clave 'action' explícita, darle prioridad
        if (isset($this->postData['action']) && isset($this->actionMap[$this->postData['action']])) {
            return $this->postData['action'];
        }
        // Manejo especial para acciones que dependen de un 'type'
        if (isset($this->postData['analizarMazo'])) {
            $type = $this->postData['type'] ?? 'basic';
            if ($type === 'basic')
                return 'analizarMazoBasico';
            if ($type === 'intermediate')
                return 'analizarMazoIntermedio';
            if ($type === 'advanced')
                return 'analizarMazoAvanzado';
        }
        if (isset($this->postData['RegLog'])) {
            return 'RegLog'; // El método RegLog dentro del controlador manejará el 'type'
        }

        // Si no se puede determinar una acción única o válida
        return null;
    }
}