<?php
spl_autoload_register(function ($class) {
    // Directorio base del proyecto (dinámico)
    $base_dir = __DIR__ . '/../../';

    // Reemplaza los separadores de namespace con separadores de directorio
    $file = str_replace('\\', '/', $class) . '.php';

    // Crea la ruta absoluta al archivo
    $absolute_path = $base_dir . $file;

    // Si el archivo existe, cárgalo
    if (file_exists($absolute_path)) {
        require_once $absolute_path;
    } else {
        throw new Exception('Class not found: ' . $class . ' in: ' . $absolute_path);
    }
});
