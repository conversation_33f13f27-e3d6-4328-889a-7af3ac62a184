<?php if ($data['TypeAcount'] != 'invitado') { ?>
    <script>
        LemonSqueezy.Setup({
            eventHandler: (event) => {
                if (event.event === "Checkout.Success") {
                    console.log(event);
                    const orderData = event.data;
                    $('#div_alert_shop_gems').html("<span class='cs-color-VibrantTurquoise text-center'>Pago realizado con éxito</span><br><span class='cs-color-GoldenYellow text-center'>Solicitando el Producto..</span><br>");
                    api({ LemonSqueezy: JSON.stringify(orderData) }, "get-gem", null, null);
                }
                if (event.event === "PaymentMethodUpdate.Mounted") {
                    console.log(event);
                }

                if (event.event === "PaymentMethodUpdate.Closed") {
                    console.log(event);
                }
            }
        });
    </script>
<?php } ?>

<h1 class="cs-color-GoldenYellow">Tienda</h1>

<h2 class="cs-color-GoldenYellow">Gemas</h2>
<div id="div_tienda_gemas" class="cs-product-container cs-product-container--primary">
    <?php foreach ($data["products"]['Gems'] as $product) { ?>
        <div class="cs-product cs-product--medium cs-product--default">
            <p class="cs-product__title"><?php echo $product->title; ?></p>
            <p class="cs-product__amound"><?php echo $product->amount; ?></p>
            <img class="cs-product__img" src="./Frontend/static/media/styles/icons/<?php echo $product->id; ?>.webp"
                alt="<?php echo $product->name; ?>">
            <a class="cs-btn cs-btn--medium cs-btn--primary lemonsqueezy-button"
                onclick="LemonSqueezy.Url.Open('<?php echo $product->link; ?>');"><?php echo $product->price; ?><?php echo $product->currency_price == 'USD' ? '$' : $product->currency_price; ?></a>
        </div>
    <?php } ?>
</div>
<div id="div_alert_shop_gems" class="text-center"></div>

<div id="div_pay_get_gems" class="cs-color-DeepBlue" style="display: none;">
    <h3>Comprar Gemas</h3>
    <p class="cs-color-DeepBlue">Comprar: <span id="span_precio_gems" class="cs-color-DeepBlue"></span><img
            class="cs-icon cs-icon--medium" src="./Frontend/static/media/styles/icons/icon_gema.webp" alt="gema"> x
        <span class="cs-color-DeepBlue" id="span_precio_get_gems"></span>$
    </p>
    <div id="paypal-button-container" class="cs-color-DeepBlue"></div>
    <div id="google-pay-button-container" class="cs-color-DeepBlue"></div>
</div>

<h2 class="cs-color-GoldenYellow">Monedas</h2>
<div id="div_tienda_monedas" class="cs-product-container cs-product-container--primary">
    <div class="alert alert--info">
        <p class="alert__message">Las monedas no estan disponibles en este momento.</p>
    </div>
    <!--     <?php foreach ($data["products"]->Coins as $value) { ?>
        <div class="cs-product cs-product--medium cs-product--default">
            <p class="cs-product__title"><?php echo $value->name; ?></p>
            <p class="cs-product__amound"><?php echo $value->amount; ?></p>
            <img class="cs-product__img" src="./<?php echo $value->image; ?>" alt="<?php echo $value->name; ?>">
            <form class="frm_get_coin">
                <input type="hidden" name="typeGetCoin" value="<?php echo $value->price->Gems; ?>">
                <button type="submit" class="cs-btn cs-btn--medium cs-btn--primary"><?php echo $value->price->Gems; ?><img
                        class="cs-icon cs-icon--medium" src="./Frontend/static/media/styles/icons/icon_gema.webp"
                        alt="gema"></button>
            </form>
        </div>
    <?php } ?> -->
</div>
<div id="div_alert_shop_gold" class="text-center"></div>

<div class="m-0-25 text-center">
    <a class="cs-link cs-link--default m-0-25" href='./TermsService' target="_blank">Términos de Servicio</a>
    <a class="cs-link cs-link--default m-0-25" href='./RefundPolicy' target="_blank">Política de Reembolso.</a>
</div>