<?php
class ChatController
{
    /*     public function mensajes(&$res)
    {
        $connection = bdconnection();
        ob_start();

        if (isset($_POST['pre_num_row_cha'])) { //carga mensajes nuevos enviados por otros usuarios
            $sql = '(SELECT * FROM chat WHERE id_Cha >= ? ORDER BY id_Cha DESC LIMIT 20) ORDER BY id_Cha ASC';
            $offset = $_POST['pre_num_row_cha'];
        } else { //carga mensajes iniciales y mensajes guardados en bd
            $sql = '(SELECT * FROM chat WHERE id_Cha <= ? ORDER BY id_Cha DESC LIMIT 20) ORDER BY id_Cha ASC';
            $stmt0 = $connection->prepare("SELECT id_Cha FROM chat ORDER BY id_Cha DESC LIMIT 1");
            $stmt0->execute();
            $result = $stmt0->get_result();
            $ultimoChat = $result->fetch_assoc();
            $idult = $ultimoChat['id_Cha']; //Numero de mensajes en la bd
            $stmt0->close();

            $_POST['offset'] == 0 ? $offset = $idult : $offset = $_POST['offset'];
            $idcar = $offset - 20;
            $res['idcar'] = $idcar;
            $res['idult'] = $idult;
        }

        $stmt = $connection->prepare($sql); //Sacar solo 20 mensajes
        $stmt->bind_param('i', $offset);
        $stmt->execute();
        $resstmt = $stmt->get_result();
        $stmt->close();
        $res['num_messages'] = $resstmt->num_rows;

        $emotes = file_get_contents('../Data/emotes.json');
        $emotesArray = json_decode($emotes);
        $pos_val = array();

        foreach ($emotesArray->emotes as $clave => $value) {
            $pos_val[] = $value->name . '.webp';
        }

        //############################### while emsajes ##############################
        while ($chat = $resstmt->fetch_assoc()) {
            $query2 = mysqli_prepare($connection, "SELECT * FROM users where id_Usu = ?");
            mysqli_stmt_bind_param($query2, "i", $chat['id_UsCh']);
            mysqli_stmt_execute($query2);
            $resque = mysqli_stmt_get_result($query2);
            $usech = mysqli_fetch_array($resque);
            mysqli_stmt_close($query2);

            if ($chat['id_MsgRep'] !== null) {
                $sqlrep = $connection->prepare("SELECT * FROM chat WHERE id_Cha = ?");
                $sqlrep->bind_param("i", $chat["id_MsgRep"]);
                $sqlrep->execute();
                $result = $sqlrep->get_result();
                $chatreply = $result->fetch_assoc();
                $sqlrep->close();

                $sqlusurep = $connection->prepare("SELECT * FROM users WHERE id_Usu = ?");
                $sqlusurep->bind_param("i", $chatreply["id_UsCh"]);
                $sqlusurep->execute();
                $result2 = $sqlusurep->get_result();
                $usurep = $result2->fetch_assoc();
                $sqlusurep->close();

                $local_datetime_chatreply = toDateLocal($chatreply['Fecha']);
            }

            $local_datetime = toDateLocal($chat['Fecha']);
            $textoChat = toLink($chat['Mensaje']); //convert to link
            $textoChat = toUserMencion($textoChat); //convert to mencion
?>

            <div id="<?php echo $chat['id_Cha']; ?>" class="MsgChat <?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "mensajes_usu" : "mensajes";
                                                                    echo (in_array($chat['Mensaje'], $pos_val) && $chat['id_MsgRep'] == null) ? " mensaje_transparent" : null; ?>" data-idcha="<?php echo $chat['id_Cha']; ?>">
                <div class="div_logo perfil_pub">
                    <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $usech['Avatar']; ?>" alt="avatar">&nbsp;
                    <span class="a_nom_men" data-name="<?php echo $usech['Usuario']; ?>" data-idpubusu="<?php echo $usech['id_Usu']; ?>"><?php echo $usech['Nombre'];
                                                                                                                                            if ($usech['Verificado'] == true) {
                                                                                                                                                echo '<img id="img_logo_verificado" src="./Frontend/static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                                                                                                                                            } ?></span>
                    <div class="div_fecha"><span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_fec_men_usu" : "span_fec_men"; ?>"><?php echo $local_datetime->format('d-m-Y H:i'); ?></span></div></br>
                </div>

                <?php if ($chat['id_MsgRep'] !== null) { //mensaje respuesta del usuario
                    if (in_array($chatreply['Mensaje'], $pos_val)) { //mensajes de respuesta a un emote 
                ?>
                        <?php if (in_array($chat['Mensaje'], $pos_val)) { //mensaje de respuesta emote 
                        ?>
                            <div id="<?php echo $chat['id_Cha']; ?>" class="div_mensaje">
                                <div id="div_res_men-<?php echo $chatreply['id_Cha']; ?>" class="div_res_men" data-idrescha="<?php echo $chatreply['id_Cha']; ?>">
                                    <div class="div_logo perfil_pub">
                                        <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $usurep['Avatar']; ?>">&nbsp;
                                        <span class="a_nom_men" data-name="<?php echo $usurep['Usuario']; ?>" data-idpubusu="<?php echo $usurep['id_Usu']; ?>"><?php echo $usurep['Nombre'];
                                                                                                                                                                if ($usurep['Verificado'] == true) {
                                                                                                                                                                    echo '<img id="img_logo_verificado" src="./Frontend/static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                                                                                                                                                                } ?></span>
                                        <div class="div_fecha"><span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_fec_men_usu" : "span_fec_men"; ?>"><?php echo $local_datetime_chatreply->format('d-m-Y H:i'); ?></span></div></br>
                                    </div>
                                    <img class="img_emote_chat_reply" src="./Frontend/static/media/emotes/<?php echo $chatreply['Mensaje']; ?>" alt="avatar">
                                </div>
                                <img class="img_emote_chat" src="./Frontend/static/media/emotes/<?php echo $chat['Mensaje']; ?>" alt="avatar">
                            </div>
                        <?php } else { //mensaje de repuesta en texto 
                        ?>
                            <div id="<?php echo $chat['id_Cha']; ?>" class="div_mensaje">
                                <div id="div_res_men-<?php echo $chatreply['id_Cha']; ?>" class="div_res_men" data-idrescha="<?php echo $chatreply['id_Cha']; ?>">
                                    <div class="div_logo perfil_pub">
                                        <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $usurep['Avatar']; ?>" alt="avatar">&nbsp;
                                        <span class="a_nom_men" data-name="<?php echo $usurep['Usuario']; ?>" data-idpubusu="<?php echo $usurep['id_Usu']; ?>"><?php echo $usurep['Nombre'];
                                                                                                                                                                if ($usurep['Verificado'] == true) {
                                                                                                                                                                    echo '<img id="img_logo_verificado" src="./Frontend/static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                                                                                                                                                                } ?></span>
                                        <div class="div_fecha"><span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_fec_men_usu" : "span_fec_men"; ?>"><?php echo $local_datetime_chatreply->format('d-m-Y H:i'); ?></span></div></br>
                                    </div>
                                    <img class="img_emote_chat_reply" src="./Frontend/static/media/emotes/<?php echo $chatreply['Mensaje']; ?>" alt="avatar">
                                </div>
                                <span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_mensaje_usu" : "span_mensaje"; ?>"><?php echo $textoChat ?></span>
                            </div>
                        <?php } ?>

                    <?php } else { //mensaje de respuesta a un texto 
                    ?>
                        <?php if (in_array($chat['Mensaje'], $pos_val)) { //mensaje de respuesta emote 
                        ?>
                            <div id="<?php echo $chat['id_Cha']; ?>" class="div_mensaje">
                                <div id="div_res_men-<?php echo $chatreply['id_Cha']; ?>" class="div_res_men" data-idrescha="<?php echo $chatreply['id_Cha']; ?>">
                                    <div class="div_logo perfil_pub">
                                        <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $usurep['Avatar']; ?>" alt="avatar">&nbsp;
                                        <span class="a_nom_men" data-name="<?php echo $usurep['Usuario']; ?>" data-idpubusu="<?php echo $usurep['id_Usu']; ?>"><?php echo $usurep['Nombre'];
                                                                                                                                                                if ($usurep['Verificado'] == true) {
                                                                                                                                                                    echo '<img id="img_logo_verificado" src="./Frontend/static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                                                                                                                                                                } ?></span>
                                        <div class="div_fecha"><span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_fec_men_usu" : "span_fec_men"; ?>"><?php echo $local_datetime_chatreply->format('d-m-Y H:i'); ?></span></div></br>
                                    </div>
                                    <span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_mensaje_usu" : "span_mensaje"; ?>"><?php echo $chatreply['Mensaje']; ?></span>
                                </div>
                                <img class="img_emote_chat" src="./Frontend/static/media/emotes/<?php echo $chat['Mensaje']; ?>" alt="<?php echo $chat['Mensaje']; ?>">
                            </div>
                        <?php } else { //mensaje de respuesta texto
                        ?>
                            <div id="<?php echo $chat['id_Cha']; ?>" class="div_mensaje">
                                <div id="div_res_men-<?php echo $chatreply['id_Cha']; ?>" class="div_res_men" data-idrescha="<?php echo $chatreply['id_Cha']; ?>">
                                    <div class="div_logo perfil_pub">
                                        <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $usurep['Avatar']; ?>" alt="avatar">&nbsp;
                                        <span class="a_nom_men" data-name="<?php echo $usurep['Usuario']; ?>" data-idpubusu="<?php echo $usurep['id_Usu']; ?>"><?php echo $usurep['Nombre'];
                                                                                                                                                                if ($usurep['Verificado'] == true) {
                                                                                                                                                                    echo '<img id="img_logo_verificado" src="./Frontend/static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                                                                                                                                                                } ?></span>
                                        <div class="div_fecha"><span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_fec_men_usu" : "span_fec_men"; ?>"><?php echo $local_datetime_chatreply->format('d-m-Y H:i'); ?></span></div></br>
                                    </div>
                                    <span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_mensaje_usu" : "span_mensaje"; ?>"><?php echo $chatreply['Mensaje']; ?></span>
                                </div>
                                <span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_mensaje_usu" : "span_mensaje"; ?>"><?php echo $textoChat ?></span>
                            </div>
                        <?php } ?>
                    <?php } ?>

                    <?php } else { //mensajes del usuario 
                    if (in_array($chat['Mensaje'], $pos_val)) { //mensaje emote 
                    ?>
                        <div class="div_mensaje">
                            <img class="img_emote_chat" src="./Frontend/static/media/emotes/<?php echo $chat['Mensaje']; ?>" alt="<?php echo $chat['Mensaje']; ?>">
                        </div>
                    <?php } else { //mensaje texto 
                    ?>
                        <div class="div_mensaje">
                            <span class="<?php echo ($chat['id_UsCh'] == $_SESSION['id']) ? "span_mensaje_usu" : "span_mensaje"; ?>"><?php echo $textoChat ?></span>
                        </div>
                <?php }
                } ?>

                <!-- interaccion con el mensaje -->
                <div class="div_res" style="display: none;">
                    <div class="div_btns_res">
                        <button id="btn_res-<?php echo $chat['id_Cha']; ?>" class="cs-btn cs-btn--medium cs-btn--primary reply-btn cs-btn--primary" data-id="<?php echo $chat['id_Cha']; ?>" data-idMsg="<?php echo $chat['id_UsCh']; ?>"><img class="img_chat_res" src="./Frontend/static/media/styles/icons/icon_res.webp" alt="icon_res.webp"></button>
                    </div>
                </div>

            </div><br>
<?php
            $res['idchat'] = $chat['id_UsCh'];
        } //fin while
        $html = ob_get_clean();
        $res['html'] = $html;
    } */

    /*     public function registerChat()
    {
        $connection = bdconnection();
        $msg0 = htmlspecialchars($_POST['msg']);
        $msg = nl2br($msg0);
        $num_text = true;

        if (strlen($msg) > 1000) {
            $num_text = false;
            $res['error'] = true;
            $res['res'] = '<script>alert("Mensaje muy largo, solo se permite hasta 1000 caracteres por mensaje, caracteres insertados: ' . strlen($_POST['msg']) . '")</script>';
        }

        if ($num_text) {
            if ($_POST['reply'] == 'yes') { //mensaje respuesta
                $queryme = mysqli_prepare($connection, "INSERT INTO chat (id_UsCh,Mensaje,Fecha,id_MsgRep) VALUES (?,?,UTC_TIMESTAMP(),?)");
                mysqli_stmt_bind_param($queryme, "isi", $_SESSION['id'], $msg, $_POST['idcha']);
                mysqli_stmt_execute($queryme);
                mysqli_stmt_close($queryme);
            } else { //mensaje normal
                $queryme = mysqli_prepare($connection, "INSERT INTO chat (id_UsCh,Mensaje,Fecha) VALUES (?,?,UTC_TIMESTAMP())");
                mysqli_stmt_bind_param($queryme, "is", $_SESSION['id'], $msg);
                mysqli_stmt_execute($queryme);
                mysqli_stmt_close($queryme);
            }

            if (!$queryme) {
                $res['res'] = '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error, intentelo mas tarde o recargue la pagina</span>';
            }
        }
    } */
}
