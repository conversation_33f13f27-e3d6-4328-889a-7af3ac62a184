<?php

use App\Services\ClashRoyaleApi;
use App\Config\Config;
use PHPUnit\Framework\TestCase;

class ClashRoyaleApiTest extends TestCase
{
    private $ClashRoyaleApi;

    protected function setUp(): void
    {
        $apiKey = Config::getCrApiKey("local");
        $this->ClashRoyaleApi = new ClashRoyaleApi($apiKey);
    }

    public function testGetPlayerData()
    {
        $playerTag = '#PQYJ2C89L';
        $endpoints = ['profile'];

        $actualResponse = $this->ClashRoyaleApi->getPlayerData($playerTag, $endpoints);

        $this->assertIsArray($actualResponse);
        $this->assertArrayHasKey('profile', $actualResponse);
        $this->assertArrayHasKey('tag', $actualResponse['profile']);
        $this->assertEquals($playerTag, $actualResponse['profile']['tag']);
    }
}
