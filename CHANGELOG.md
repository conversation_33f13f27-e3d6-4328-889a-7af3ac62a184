# [0.7.0](https://github.com/ClashStrategic/api/compare/v0.6.2...v0.7.0) (2025-05-30)


### Bug Fixes

* **DeckController:** update default values for options ([4097599](https://github.com/ClashStrategic/api/commit/40975994d1cd8d8b8c029fc902f3da3a7bc59341))


### Features

* Supports deck building in "invitado" sessions only for the free basic level ([915efdf](https://github.com/ClashStrategic/api/commit/915efdfe35c0d6485e22b9998290cfc75e17e15d))

## [0.6.2](https://github.com/ClashStrategic/api/compare/v0.6.1...v0.6.2) (2025-05-29)


### Bug Fixes

* **Routes:** move showUserBasicData logic to correct block ([4faa1d7](https://github.com/ClashStrategic/api/commit/4faa1d705afc3e9e31f8c919687907ba9d55cd8c))

## [0.6.1](https://github.com/ClashStrategic/api/compare/v0.6.0...v0.6.1) (2025-05-29)


### Bug Fixes

* **CardSectionView:** update advanced analysis button count ([15cbb3f](https://github.com/ClashStrategic/api/commit/15cbb3fe9f57765351278e962a803d0b8a901e75))
* **CardSectionView:** update button label from Alpha to Beta ([9545285](https://github.com/ClashStrategic/api/commit/9545285e17af06289841817832f0dec2802f419f))
* **products:** update price for advanced deck analyzer ([5025de9](https://github.com/ClashStrategic/api/commit/5025de927a4599cbe8dd8ed716d0f78e9330454e))

# [0.6.0](https://github.com/ClashStrategic/api/compare/v0.5.0...v0.6.0) (2025-05-29)


### Features

* Enable advanced deck builder functionality ([4a1aefc](https://github.com/ClashStrategic/api/commit/4a1aefc2a8c8eec1a9a6c9bd0ea26bfd58f10940))
* Implement intermediate deck builder level ([2633662](https://github.com/ClashStrategic/api/commit/263366299c90b771da20c164bb09342f04efacb0))

# [0.5.0](https://github.com/ClashStrategic/api/compare/v0.4.2...v0.5.0) (2025-05-28)


### Features

* **total_gem_and_coin_users:** add view for total gems and coins ([bca00ec](https://github.com/ClashStrategic/api/commit/bca00eca87fd23ff20e4cdde4cb9500869b27761))
* **total_virtual_payments:** add view for total payments by currency ([01bac9f](https://github.com/ClashStrategic/api/commit/01bac9ffd6534cb249f3580b62f104ed24224233))

## [0.4.2](https://github.com/ClashStrategic/api/compare/v0.4.1...v0.4.2) (2025-05-27)


### Bug Fixes

* **.htaccess:** fix directory locking from php to client only ([692a373](https://github.com/ClashStrategic/api/commit/692a373dfc8a85c945c83810e4a4ca090801fcd7))

## [0.4.1](https://github.com/ClashStrategic/api/compare/v0.4.0...v0.4.1) (2025-05-27)


### Bug Fixes

* **.htaccess:** Block access to sensitive directories ([6c44a82](https://github.com/ClashStrategic/api/commit/6c44a82886abbb7b17e5b891530ff2eba9254758))
* **DeckController:** handle exception for unavailable levels ([65f457a](https://github.com/ClashStrategic/api/commit/65f457a8a1abaa877a0c9058bc37957845ac1ca8))
* **getGems:** Check the order by getting it from the Lemonsqueezy API with the object id of the jsonString sent from the client ([def6fcc](https://github.com/ClashStrategic/api/commit/def6fcc2ef765788db78cf642cc775c17574f5ca))

# [0.4.0](https://github.com/ClashStrategic/api/compare/v0.3.1...v0.4.0) (2025-05-26)


### Features

* **AboutView:** add web app version and updated date display ([8c91d37](https://github.com/ClashStrategic/api/commit/8c91d373413e11dbb9b0debc047f51e8a431f598))

## [0.3.1](https://github.com/ClashStrategic/api/compare/v0.3.0...v0.3.1) (2025-05-25)


### Bug Fixes

* **AboutView:** update version label to specify API version ([29f0422](https://github.com/ClashStrategic/api/commit/29f0422471991629bb1d02a2809aacd9f9837cc6))
* **api version:** add tooltip for API version information ([ec179b9](https://github.com/ClashStrategic/api/commit/ec179b912dc87c0eeb7e97287f97c930047aec26))
* **Config:** update getDateCS to extract api version and date ([f16e832](https://github.com/ClashStrategic/api/commit/f16e83248882c90fbd707d4cfce5bf764a570b58))
* **HomeView:** remove version query from CSS and JS links ([491ac04](https://github.com/ClashStrategic/api/commit/491ac04e33b5020dc5a922d1f98d5734885d754d))
* Rename the "Actualizado el" label to "Api Actualizado el" in the About view. ([bf8a052](https://github.com/ClashStrategic/api/commit/bf8a0526d80d6f5b19b24a06a22a28b3fec966c6))

# [0.3.0](https://github.com/ClashStrategic/api/compare/v0.2.0...v0.3.0) (2025-05-23)


### Features

* **tools:** Move deck analysis and builder tools ([69b4203](https://github.com/ClashStrategic/api/commit/69b42036d3c3572a71623d8c6aea33fe0f29ede9))

# [0.2.0](https://github.com/ClashStrategic/api/compare/v0.1.2...v0.2.0) (2025-05-22)


### Bug Fixes

* **.htaccess:** add URL rewriting and access rules ([c3fa2a6](https://github.com/ClashStrategic/api/commit/c3fa2a68e8fa9a6df595f465fd61d9ace14fb9d6))
* **HomeView:** add async attribute to main.js script ([9c7693d](https://github.com/ClashStrategic/api/commit/9c7693dd84cd8eb71448ee22cf21e63eda0806b0))
* **Router:** improve error handling messages in response ([429508e](https://github.com/ClashStrategic/api/commit/429508ef4166a50fa9c6789641a838ff2ddb6f39))
* **Routes:** correct redirect paths for localhost ([637a572](https://github.com/ClashStrategic/api/commit/637a572aac26cdc9bc1183fd5dbbb56dbea17232))


### Features

* **Router:** simplify response handling for routes ([8be0677](https://github.com/ClashStrategic/api/commit/8be0677c75837f8f75b9fc7e6a29f502f5833535))

## [0.1.2](https://github.com/ClashStrategic/api/compare/v0.1.1...v0.1.2) (2025-05-21)


### Bug Fixes

* Remove old application entry points and static files ([70139c8](https://github.com/ClashStrategic/api/commit/70139c8ce4d1eb1fad02e3100b2b5327893885e7))

## [0.1.1](https://github.com/ClashStrategic/api/compare/v0.1.0...v0.1.1) (2025-05-21)


### Bug Fixes

* remove unused robots.txt and sitemap.xml ([c8cb674](https://github.com/ClashStrategic/api/commit/c8cb6747f63eebb88f3f6fdb04cf8e46ed48af2e))
