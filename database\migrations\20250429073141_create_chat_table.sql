CREATE TABLE `chat` (
  `id_Cha` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
  `id_UsCh` INT NOT NULL,
  `id_MsgRep` INT DEFAULT NULL,
  `Mensaje` VARCHAR(1500) NOT NULL,
  `Fecha` DATETIME NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `fk_Cha_Usu` FOREIGN KEY (`id_UsCh`) REFERENCES `users` (`id_Usu`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;