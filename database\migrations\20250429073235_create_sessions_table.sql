CREATE TABLE
  `sessions` (
    `id_Ses` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsSe` INT NOT NULL,
    `Session` VARCHAR(15) DEFAULT 'Activa',
    `IP` VARCHAR(50),
    `Token` VARCHAR(32) NOT NULL,
    `UserAgent` VARCHAR(255) NUll,
    `Fec_ini` DATETIME NOT NULL,
    `Fec_sal` DATETIME NUll,
    CONSTRAINT `fk_Ses_Usu` FOREIGN KEY (`id_UsSe`) REFERENCES `users` (`id_Usu`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;