CREATE TABLE
  `conditions` (
    `id_Con` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_StCo` INT NOT NULL,
    `Condicion` JSON NOT NULL,
    `<PERSON>untos` FLOAT NOT NULL,
    `<PERSON><PERSON>je` VARCHAR(250) NOT NULL,
    `<PERSON><PERSON>o` DATETIME NOT NULL,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Con_Str` FOREIGN KEY (`id_StCo`) REFERENCES `strategies` (`id_Str`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;