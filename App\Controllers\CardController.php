<?php

namespace App\Controllers;

use App\Models\{Card};
use App\Views\View;

class CardController
{
    public static function showAll()
    {
        $View = new View('ShowCards');
        return $View->render();
    }

    // Función para calcular las estadísticas (daño o puntos de vida) de un nivel dado
    public function calcularEstadistica($valorInicial, $nivelActual, $nivelObjetivo, $multiplicadores)
    {
        // Si el nivel objetivo es menor que el nivel actual, devolvemos el valor inicial
        if ($nivelObjetivo < $nivelActual) {
            return $valorInicial;
        }
        // Inicializamos el valor con el valor inicial
        $valor = $valorInicial;
        // Recorremos desde el nivel actual hasta el nivel objetivo - 1
        for ($nivel = $nivelActual; $nivel < $nivelObjetivo; $nivel++) {
            // Obtenemos el multiplicador para el nivel actual
            $multiplicador = isset($multiplicadores[$nivel]) ? $multiplicadores[$nivel] : 1;
            // Aplicamos el multiplicador
            $valor *= $multiplicador;
        }
        return round($valor);
    }

    public function calculateAllLevels($initialValue, $initialLevel, $maxLevel, $multipliers)
    {

        /* test:
            $multiplicadores = [
        1 => 1.0886, // Multiplicador para pasar del nivel 1 al 2
        2 => 1.1047, // Multiplicador para pasar del nivel 2 al 3
        3 => 1.1053, // Multiplicador para pasar del nivel 3 al 4
        4 => 1.0952,
        5 => 1.0957,
        6 => 1.1032,
        7 => 1.0935,
        8 => 1.0987,
        9 => 1.1018,
        10 => 1.0978,
        11 => 1.0941,
        12 => 1.1041,
        13 => 1.0943,
        14 => 1.0977,
    ];
    $res['test'] = $CardController->calcularTodosLosNiveles(47, 1, 15, $multiplicadores);
        */
        $resultados = [];
        for ($nivel = $initialLevel; $nivel <= $maxLevel; $nivel++) {
            $resultados["level" . $nivel] = $this->calcularEstadistica($initialValue, $initialLevel, $nivel, $multipliers);
        }
        return $resultados;
    }

    public function infcards() //inf cards
    {
        $res = ["state" => "inprogress", "res" => "", "alerts" => []];
        $json = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
        $jsonMedia = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/mediaCards.json'));

        ($_POST['type'] == 'tower') ? $cardsjson = $json->towerCards : $cardsjson = $json->cards;

        foreach ($cardsjson as $card) {
            if ($card->name == $_POST['name']) {
                $View = new View('StatsCardView', ['card' => $card, 'jsonMedia' => $jsonMedia]);
                $res['html'] = $View->render();
                $res['state'] = 'success';
                break;
            }
        }
        return $res;
    }
}
