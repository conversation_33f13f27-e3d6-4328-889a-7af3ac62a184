CREATE TABLE
  `votes` (
    `id_Vot` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsVo` INT NOT NULL,
    `id_EnVo` INT NULL,
    `Voto` INT DEFAULT 0,
    `Opcion` INT null,
    `<PERSON><PERSON>` DATETIME NOT NULL,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Vot_Usu` FOREIGN KEY (`id_UsVo`) REFERENCES `users` (`id_Usu`),
    CONSTRAINT `fk_Vot_Enc` FOREIGN KEY (`id_EnVo`) REFERENCES `surveys` (`id_Enc`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;