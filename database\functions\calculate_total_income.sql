-- Calcula el ingreso total (en USD) de pagos completados.
DROP FUNCTION IF EXISTS calculate_total_income;

-- Establece el delimitador personalizado en su propia línea
DELIMITER //
-- Definición de la función en líneas separadas y claras
CREATE FUNCTION calculate_total_income () RETURNS DECIMAL(15, 2) DETERMINISTIC READS SQL DATA BEGIN DECLARE v_total_income DECIMAL(15, 2) DEFAULT 0.00;

SELECT
    COALESCE(SUM(price), 0.00) INTO v_total_income
FROM
    payments
WHERE
    status = 'completed';

RETURN v_total_income;

END //
-- Restaura el delimitador estándar en su propia línea
DELIMITER ;

-- --- <PERSON> usarla (ejemplo) ---
-- SELECT calculate_total_income();