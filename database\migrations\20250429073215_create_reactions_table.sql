CREATE TABLE
  `reactions` (
    `id_Rea` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsRe` INT NOT NULL,
    `id_PuRe` INT NOT NULL,
    `<PERSON><PERSON>cion` INT DEFAULT '0',
    `<PERSON><PERSON>` DATETIME NOT NULL,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Rea_Usu` FOREIGN KEY (`id_UsRe`) REFERENCES `users` (`id_Usu`),
    CONSTRAINT `fk_Rea_Pub` FOREIGN KEY (`id_PuRe`) REFERENCES `publications` (`id_Pub`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;