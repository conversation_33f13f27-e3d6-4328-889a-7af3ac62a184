CREATE TABLE
  `surveys` (
    `id_Enc` INT AUTO_INCREMENT PRIMARY KEY,
    `id_UsEn` INT NOT NULL,
    `<PERSON>cha` DATETIME NOT NULL,
    `Fecha_exp` DATETIME NOT NULL,
    `Pregunta` VARCHAR(500) NULL,
    `Imgpreg` VARCHAR(20) NULL,
    `Opcion1` VARCHAR(500) NULL,
    `Imagen1` VARCHAR(20) NULL,
    `Votos1` INT default 0,
    `Opcion2` VARCHAR(500) NULL,
    `Imagen2` VARCHAR(20) NULL,
    `Votos2` INT default 0,
    `Opcion3` VARCHAR(500) NULL,
    `Imagen3` VARCHAR(20) NULL,
    `Votos3` INT default 0,
    `Opcion4` VARCHAR(500) NULL,
    `Imagen4` VARCHAR(20) NULL,
    `Votos4` INT default 0,
    `Opcion5` VARCHAR(500) NULL,
    `Imagen5` VARCHAR(20) NULL,
    `Votos5` INT default 0,
    `Opcion6` VARCHAR(500) NULL,
    `Imagen6` VARCHAR(20) NULL,
    `Votos6` INT default 0,
    `Opcion7` VARCHAR(500) NULL,
    `Imagen7` VARCHAR(20) NULL,
    `Votos7` INT default 0,
    `Opcion8` VARCHAR(500) NULL,
    `Imagen8` VARCHAR(20) NULL,
    `Votos8` INT default 0,
    `Opcion9` VARCHAR(500) NULL,
    `Imagen9` VARCHAR(20) NULL,
    `Votos9` INT default 0,
    `Opcion10` VARCHAR(500) NULL,
    `Imagen10` VARCHAR(20) NULL,
    `Votos10` INT default 0,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Enc_Usu` FOREIGN KEY (`id_UsEn`) REFERENCES `users` (`id_Usu`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;