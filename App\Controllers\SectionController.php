<?php

namespace App\Controllers;

use App\Views\View;

class SectionController
{
    private $connectionDB;

    public function __construct(\PDO $connectionDB = null)
    {
        $this->connectionDB = $connectionDB;
    }

    public function show($type)
    {
        $res = ['state' => 'inprogress', 'alerts' => [], 'res' => ''];
        if ($type == 'shop') {
            $ProductController = new ProductController($this->connectionDB);
            $View = new View(
                'ShopSectionView',
                [
                    'TypeAcount' => $_SESSION['TypeAcount'],
                    "products" => ["Gems" => $ProductController->getAll("gems", "CURRENCY")]
                ]
            );
            $res['state'] = 'success';
            $res['res'] = $View->render();
        } elseif ($type == 'cartas') {
            $View = new View('CardSectionView');
            $res['state'] = 'success';
            $res['res'] = $View->render();
        } elseif ($type == 'puclication') {
        } elseif ($type == 'ch4t') {
        } else {
            $res['state'] = 'error';
            $res['res'] = 'Sectión "' . $type . '" Not Found';
        }
        return $res;
    }
}
