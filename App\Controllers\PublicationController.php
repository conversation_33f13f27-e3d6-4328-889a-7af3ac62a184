<?php

namespace App\Controllers;

use App\Config\Config;

class PublicationController
{

    /*     public function publicaciones(&$res)
        {
            $connection = bdconnection();
            if ($_POST['typePub'] == 'pubUsu') { //carga pub y enc de un usuario
                $sqlPub = "SELECT * FROM publications WHERE id_UsPu = ? ORDER BY Fecha DESC LIMIT 50";
                $sqlEnc = "SELECT * FROM surveys WHERE id_UsEn = ? ORDER BY Fecha DESC LIMIT 15";
                $limit = $_POST['idpubusu'];
            } elseif ($_POST['typePub'] == 'pubAll') { //carga de pub y enc para la seccion publicacion
                $sqlPub = "SELECT * FROM publications ORDER BY Fecha DESC LIMIT ?";
                $sqlEnc = "SELECT * FROM surveys ORDER BY Fecha DESC LIMIT ? ";
                $limit = 100;
            } else {
                $res['state'] = 'error';
                return;
            }

            $conPub = $connection->prepare($sqlPub); //consulta a publicaciones
            $conPub->bind_param('i', $limit);
            $conPub->execute();
            $rescon = $conPub->get_result();
            $pubnum = $conPub->num_rows();

            $conEnc = $connection->prepare($sqlEnc); //consulta a encuesta
            $conEnc->bind_param('i', $limit);
            $conEnc->execute();
            $resenc = $conEnc->get_result();
            $pubnum = $conPub->num_rows();

            $pubEncArr = array(); //array para publicaciones y encuestas
            while ($publis = $rescon->fetch_assoc()) {
                $pubEncArr[] = $publis;
            }
            while ($encc = $resenc->fetch_assoc()) {
                $pubEncArr[] = $encc;
            }

            $conPub->close();
            $conEnc->close();

            usort($pubEncArr, function ($a, $b) { //ordenar de forma decendiente segun la fecha
                return strtotime($b['Fecha']) - strtotime($a['Fecha']);
            });

            ob_start();
            foreach ($pubEncArr as $dato) {
                if (isset($dato['id_Pub'])) { //publicaciones
                    $usuariob = mysqli_prepare($connection, "SELECT * FROM users where id_Usu = ?");
                    mysqli_stmt_bind_param($usuariob, "i", $dato['id_UsPu']);
                    mysqli_stmt_execute($usuariob);
                    $resuseb = mysqli_stmt_get_result($usuariob);
                    mysqli_stmt_close($usuariob);
                    $use = mysqli_fetch_array($resuseb);

                    $nomarc = explode(",", $dato['Archivo']);
                    $imagenes_por_pagina = 1;
                    $cantidad_imagenes = count($nomarc);
                    $cantidad_paginas = ceil($cantidad_imagenes / $imagenes_por_pagina);
                    $pagina_actual = isset($_GET['pagina']) ? $_GET['pagina'] : 1;
                    $indice_inicio = ($pagina_actual - 1) * $imagenes_por_pagina;
                    $imagenes_pagina = array_slice($nomarc, $indice_inicio, $imagenes_por_pagina);

                    $local_datetime = toDateLocal($dato['Fecha']);
                    $textoChat = toLink($dato['Contenido']);
                    $textoChat = toUserMencion($textoChat);
                    ?>
                    <div class="publicacion" id="Publi-<?php echo $dato['id_Pub']; ?>" data-idusu="<?php echo $dato['id_UsPu'] ?>">
                        <div class="caja_pub text-left m-1">
                            <div class="perfil_pub">
                                <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $use['Avatar']; ?>"
                                    alt="<?php echo $use['Avatar']; ?>"><span style="white-space: pre"> </span>
                                <span class="a_nom_pub" data-name="<?php echo $use['Usuario']; ?>"
                                    data-idpubusu="<?php echo $use['id_Usu']; ?>"><?php echo $use['Nombre'];
                                       if ($use['Verificado'] == true) {
                                           echo '<img id="img_logo_verificado" src="./Frontend/static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                                       } ?></span><span style="white-space: pre"> </span>
                                <div class="div_fecha"><span class="span_fec_pub"><?php echo $local_datetime->format('d-m-Y'); ?></span>
                                </div></br>
                            </div>

                            <?php if ($textoChat != '') { ?>
                                <p class="p_publi"><?php echo $textoChat; ?></p>
                            <?php }

                            if ($dato['Archivo'] != "") {
                                if (strpos($dato['Archivo'], '.jpeg') !== false || strpos($dato['Archivo'], '.webp') !== false || strpos($dato['Archivo'], '.jpg') !== false || strpos($dato['Archivo'], '.svg') !== false || strpos($dato['Archivo'], '.jfif') !== false || strpos($dato['Archivo'], '.webp') !== false || strpos($dato['Archivo'], '.bmp') !== false || strpos($dato['Archivo'], '.gif') !== false) { ?>
                                    <div id="gallery-container" class="gallery-container" data-idpub="<?php echo $dato['id_Pub']; ?>">
                                        <div class="gallery" data-idpub="<?php echo $dato['id_Pub']; ?>">
                                            <?php foreach ($imagenes_pagina as $nombre) { ?>
                                                <figure class="fg_img_pub">
                                                    <img data-idpub="Publi-<?php echo $dato['id_Pub']; ?>" class="pub_img"
                                                        data-src="./Frontend/static/media/styles/user/publicaciones/imagenes/<?php echo $nombre; ?>"
                                                        alt="<?php echo $nombre; ?>">
                                                </figure>
                                            <?php } ?>
                                        </div>
                                    </div>

                                    <div id="pagination-container" class="pagination-container" data-database="pub"
                                        data-pagina="<?php echo $pagina_actual; ?>" data-idpub="Publi-<?php echo $dato['id_Pub']; ?>">
                                        <?php if ($cantidad_paginas > 1) { ?>
                                            <div class="pagination">
                                                <?php for ($i = 1; $i <= $cantidad_paginas; $i++) { ?>
                                                    <span id="span-pag-<?php echo $dato['id_Pub']; ?>-<?php echo $i; ?>"
                                                        data-idpub="<?php echo $dato['id_Pub']; ?>" data-database="pub" data-pagina="<?php echo $i; ?>"
                                                        class="span-pag<?php echo ($i == $pagina_actual) ? ' active' : null; ?>"><?php echo $i; ?></span>
                                                <?php } ?>
                                            </div>
                                        <?php } ?>
                                    </div>

                                    <?php
                                }
                            } ?>
                        </div>
                    </div>

                    <?php
                    //Reacciones
                    $sqlreac = mysqli_prepare($connection, "SELECT * FROM reactions WHERE id_PuRe=? AND id_UsRe = ?");
                    mysqli_stmt_bind_param($sqlreac, "ii", $dato['id_Pub'], $_SESSION['id']);
                    mysqli_stmt_execute($sqlreac);
                    $resreac = mysqli_stmt_get_result($sqlreac);
                    $resreacarr = mysqli_fetch_assoc($resreac);
                    $rowsreact = mysqli_num_rows($resreac);
                    mysqli_stmt_close($sqlreac);
                    ?>

                    <div class="div_int">
                        <form class="reaccion frm_reac" data-ajax-success="ins-rea">
                            <input type="hidden" class="inp_reac1" name="reac" value="1">
                            <input type="hidden" class="inp_reac1_idpub" name="id_pub" value="<?php echo $dato['id_Pub']; ?>">
                            <button type="submit" id="reaccion1-<?php echo $dato['id_Pub']; ?>" class="btn_reac1"
                                data-idreac="<?php echo $dato['id_Pub']; ?>">
                                <img id="img_reac1-<?php echo $dato['id_Pub']; ?>"
                                    class="emogi_reaccion <?php echo ($rowsreact == 1 && $resreacarr['Reaccion'] == 1) ? "emogi_click" : null; ?>"
                                    src="./Frontend/static/media/styles/reacciones/reac_1.webp" alt="reaccion1">
                                <span class="span_num_reac1 text"
                                    id="nuevareac1-<?php echo $dato['id_Pub']; ?>"><?php echo $dato['Reaccion1']; ?></span>
                            </button>
                        </form>

                        <form class="reaccion frm_reac" data-ajax-success="ins-rea">
                            <input type="hidden" class="inp_reac2" name="reac" value="2">
                            <input type="hidden" class="inp_reac2_idpub" name="id_pub" value="<?php echo $dato['id_Pub']; ?>">
                            <button type="submit" id="reaccion2-<?php echo $dato['id_Pub']; ?>" class="btn_reac2"
                                data-idreac="<?php echo $dato['id_Pub']; ?>">
                                <img id="img_reac2-<?php echo $dato['id_Pub']; ?>"
                                    class="emogi_reaccion <?php echo ($rowsreact == 1 && $resreacarr['Reaccion'] == 2) ? "emogi_click" : null; ?>"
                                    src="./Frontend/static/media/styles/reacciones/reac_2.webp" alt="reaccion2">
                                <span class="span_num_reac2 text"
                                    id="nuevareac2-<?php echo $dato['id_Pub']; ?>"><?php echo $dato['Reaccion2']; ?></span>
                            </button>
                        </form>

                        <form class="reaccion frm_reac" data-ajax-success="ins-rea">
                            <input type="hidden" class="inp_reac3" name="reac" value="3">
                            <input type="hidden" class="inp_reac3_idpub" name="id_pub" value="<?php echo $dato['id_Pub']; ?>">
                            <button type="submit" id="reaccion3-<?php echo $dato['id_Pub']; ?>" class="btn_reac3"
                                data-idreac="<?php echo $dato['id_Pub']; ?>">
                                <img id="img_reac3-<?php echo $dato['id_Pub']; ?>"
                                    class="emogi_reaccion <?php echo ($rowsreact == 1 && $resreacarr['Reaccion'] == 3) ? "emogi_click" : null; ?>"
                                    src="./Frontend/static/media/styles/reacciones/reac_3.webp" alt="reaccion3">
                                <span class="span_num_reac3 text"
                                    id="nuevareac3-<?php echo $dato['id_Pub']; ?>"><?php echo $dato['Reaccion3']; ?></span>
                            </button>
                        </form>

                        <form class="reaccion frm_reac" data-ajax-success="ins-rea">
                            <input type="hidden" class="inp_reac4" name="reac" value="4">
                            <input type="hidden" class="inp_reac4_idpub" name="id_pub" value="<?php echo $dato['id_Pub']; ?>">
                            <button type="submit" id="reaccion4-<?php echo $dato['id_Pub']; ?>" class="btn_reac4"
                                data-idreac="<?php echo $dato['id_Pub']; ?>">
                                <img id="img_reac4-<?php echo $dato['id_Pub']; ?>"
                                    class="emogi_reaccion <?php echo ($rowsreact == 1 && $resreacarr['Reaccion'] == 4) ? "emogi_click" : null; ?>"
                                    src="./Frontend/static/media/styles/reacciones/reac_4.webp" alt="reaccion4">
                                <span class="span_num_reac4 text"
                                    id="nuevareac4-<?php echo $dato['id_Pub']; ?>"><?php echo $dato['Reaccion4']; ?></span>
                            </button>
                        </form>

                        <!--################################### Comentario ##########################################-->
                        <button class="btn_comentarios border-0 bg-transparent" data-idpub="<?php echo $dato['id_Pub']; ?>"
                            data-getcom="no">
                            <img class="emogi_comentario" src="./Frontend/static/media/styles/reacciones/comentarios.webp"
                                alt="comentarios">&nbsp;&nbsp;<span class="span_num_reac4 text"><?php echo $dato['Comentarios']; ?></span>
                        </button>

                        <div class="div_coment" style="display: none;">
                            <p>Comentarios</p>
                            <hr>
                            <div class="caja-coment">
                                <div class="nuevocoment"></div><br>
                            </div>

                            <div class="input_comentar">
                                <hr>
                                <form class="frm_Comentar" autocomplete="off" data-idpub="<?php echo $dato['id_Pub']; ?>"
                                    data-idusu="<?php echo $dato['id_UsPu']; ?>" data-nameusu="<?php echo $use['Nombre']; ?>">
                                    <input type="text" class="txt_comentar" placeholder="Escribe un comentario" name="Comentario"
                                        required></input>
                                    <button type="submit" name="Comentar" class="m-a"><img class="cs-icon cs-icon--medium"
                                            src="././Frontend/static/media/styles/icons/icon-enviar.webp" alt="enviar"></button>
                                </form>
                                <div id="div_com_res"></div>
                            </div>
                        </div>

                    </div><br>

                    <?php
                } else {
                    //################################## Encuesta ####################################
                    $sqlusuenc = mysqli_prepare($connection, "SELECT * FROM users WHERE id_Usu = ?");
                    mysqli_stmt_bind_param($sqlusuenc, "i", $dato['id_UsEn']);
                    mysqli_stmt_execute($sqlusuenc);
                    $resusuenc = mysqli_stmt_get_result($sqlusuenc);
                    mysqli_stmt_close($sqlusuenc);
                    $useenc = mysqli_fetch_array($resusuenc);

                    $sqlvotousu = mysqli_prepare($connection, "SELECT * FROM votes WHERE id_UsVo = ? AND id_EnVo = ?");
                    mysqli_stmt_bind_param($sqlvotousu, "ii", $_SESSION['id'], $dato['id_Enc']);
                    mysqli_stmt_execute($sqlvotousu);
                    $resvotousu = mysqli_stmt_get_result($sqlvotousu);
                    mysqli_stmt_close($sqlvotousu);
                    $usuvoto = mysqli_fetch_array($resvotousu);

                    $local_datetime_enc = Config::toDateLocal($dato['Fecha']);

                    $textoChat_enc = $dato['Pregunta'];
                    $pattern_enc = '/((http(s)?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-]*)*\/?)/';
                    $textoChat_enc = preg_replace($pattern_enc, '<a class="cs-link cs-link--default" href="$0" target="_blank">$0</a>', $textoChat_enc);

                    ?>
                    <div class="publicacion pb-2" id="Enc-<?php echo $dato['id_Enc']; ?>">
                        <div class="caja_pub text-left m-1">
                            <div class="perfil_pub">
                                <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $useenc['Avatar']; ?>"
                                    alt="<?php echo $useenc['Avatar']; ?>"><span style="white-space: pre"> </span>
                                <span class="a_nom_pub" data-name="<?php echo $useenc['Usuario']; ?>"
                                    data-idpubusu="<?php echo $useenc['id_Usu']; ?>"><?php echo $useenc['Nombre'];
                                       if ($useenc['Verificado'] == true) {
                                           echo '<img id="img_logo_verificado" src="./Frontend/static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                                       } ?></span><span style="white-space: pre"> </span>
                                <div class="div_fecha"><span class="span_fec_pub"><?php echo $local_datetime_enc->format('d-m-Y'); ?></span>
                                </div></br>
                            </div>
                            <?php if ($textoChat_enc != '') { ?>
                                <p class="text"><?php echo $textoChat_enc; ?></p>
                            <?php }

                            $total = 0;
                            for ($i = 1; $i <= 3; $i++) {
                                $total += $dato['Votos' . $i];
                            }

                            for ($i = 1; $i <= 3; $i++) {
                                if (isset($usuvoto)) { // || strtotime(date('Y-m-d H:i:s')) >= strtotime($dato['Fecha_exp']) si el usuario voto en la encuesta o la encuesta ha terminado
                                    $votos = (strtotime(date('Y-m-d H:i:s')) >= strtotime($dato['Fecha_exp']) || (isset($usuvoto) && $usuvoto['Voto'] == 1)) ? '<div id="opcion' . $i . '" class="div_num_votos cs-color-LightGrey"><span class="span_num_vot">' . round($dato['Votos' . $i] / $total * 100) . '%</span></div>' : null;
                                    $select_option_enc = (isset($usuvoto) && $usuvoto['Opcion'] == $i) ? 'select_option_enc' : null;
                                } else {
                                    $votos = null;
                                    $select_option_enc = null;
                                }
                                echo ($dato['Imagen' . $i] != null) ? '<img class="img_opcion_enc" src="./Frontend/static/media/encuesta/' . $dato['Imagen' . $i] . '" alt="' . $dato['Imagen' . $i] . '">' : null;
                                echo ($dato['Opcion' . $i] != null) ? '<form class="frm_opc_enc" data-ajax-success="opc-enc"><input class="inp_id_enc" type="hidden" name="id_Enc" value="' . $dato['id_Enc'] . '"><input class="inp_opc" type="hidden" name="opcion" value="' . $i . '"><button class="btn_opc' . $i . ' btn_opc cs-color-GoldenYellow ' . $select_option_enc . '" type="submit">' . $dato['Opcion' . $i] . $votos . '</button></form>' : null;
                            }

                            echo '<span id="span_total_enc-' . $dato['id_Enc'] . '" class="text span_total_votos"> Votos: ' . $total . '</span>';
                            echo '<span id="span_exp_enc-' . $dato['id_Enc'] . '" class="text span_exp_votos float-left">Limite: ' . ($dato['Fecha_exp'] != '0000-00-00 00:00:00' ? (strtotime(date('Y-m-d H:i:s')) >= strtotime($dato['Fecha_exp']) ? 'Ha Terminado' : date_format(toDateLocal($dato['Fecha_exp']), 'd-m H:i')) : 'Ha Terminado') . '</span>'; ?>
                        </div>
                    </div><br>
                    <?php
                }
            }
            echo empty($pubEncArr) && '<p>Sin publicaciones</p>';
            //echo ($pubnum == 100) ? '<div class="publicacion"><p class="cs-color-GoldenYellow">Solo mostramos las ultimas 100 publicaciones</p></div><br><br>' : null;
            $res['html'] = ob_get_clean();
        } */

    public function toLink($texto) //text a link
    {
        $patron = '/((http(s)?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-]*)*(\.[\w-]+)?\/?[\w\/\?\=\&\%\#\-\.\!\@\$\^\*\(\)]*)/'; // Expresión regular para encontrar URLs
        preg_match_all($patron, $texto, $coincidencias); // Buscar todas las URLs en el texto
        foreach ($coincidencias[0] as $url) { //Iterar sobre cada coincidencia
            if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) { // Verificar si el URL ya tiene http o https
                $enlace = "<a class='cs-link cs-link--default' target='_blank' href=\"$url\">$url</a>"; // Si ya tiene http o https, crear el enlace
            } else { // Si no tiene http o https, agregar https al principio
                $enlace = "<a class='cs-link cs-link--default' target='_blank' href=\"https://$url\">$url</a>";
            }
            $texto = str_replace($url, $enlace, $texto); // Reemplazar el URL original por el enlace en el texto
        }
        return $texto; // Devolver el texto con los enlaces convertidos
    }

    /*     public function toUserMencion($text) //convierte los texto con @ a nenciones de usuarios
        {
            preg_match_all('/@(\w+)/', $text, $menciones);
            $nombresUsuariosMencionados = $menciones[1];
            $text = $text; // Inicializamos $text con el texto original
            foreach ($nombresUsuariosMencionados as $usuario) {
                $enlacePerfil = '<span class="a_nom_pub" data-name="' . $usuario . '">@' . $usuario . '</span>';
                $text = str_replace('@' . $usuario, $enlacePerfil, $text);
            }
            return $text;
        } */

    /*     public function pagination(&$res) //pagination
    {
        $connection = bdconnection();

        if ($_POST['db'] == "pub") {
            $consulta = mysqli_prepare($connection, "SELECT * FROM publications WHERE id_Pub = ?");
        } else {
            $consulta = mysqli_prepare($connection, "SELECT * FROM sugerencias WHERE id_Sug = ?");
        }
        mysqli_stmt_bind_param($consulta, "i", $_POST['idpub']);
        mysqli_stmt_execute($consulta);
        $rescon = mysqli_stmt_get_result($consulta);
        mysqli_stmt_close($consulta);

        $archivos = mysqli_fetch_array($rescon);
        $nomarc = explode(",", $archivos['Archivo']);
        $imagenes_pagina = array_slice($nomarc, $_POST['inicio'], $_POST['fin'] - $_POST['inicio']);
        $archivo = implode('', $imagenes_pagina);

        $res['archivo'] = $archivo;
    } */

    /*     public function returnComent(&$res) //retorna los comentarios de las publicaciones
    {
        $connection = bdconnection();
        $idpub = $_POST['id_Pub'];
        $comentarios1 = mysqli_query($connection, "(SELECT * FROM comments WHERE id_PuCo = $idpub ORDER BY id_Com desc) ORDER BY id_Com asc");
        $comrows = mysqli_num_rows($comentarios1);

        ob_start();
        if ($comrows == 0) {
            $res['res'] = '<p class="text inf_com">Sin Comentarios</p>';
        } else {
            while ($com = mysqli_fetch_array($comentarios1)) {
                $usuarioc = mysqli_query($connection, "SELECT * FROM users WHERE id_Usu =" . $com['id_UsCo'] . "");
                $usec = mysqli_fetch_array($usuarioc);

                $local_datetime = toDateLocal($com['Fecha']);
                $textoChat = toLink($com['Comentario']);

                if ($com['id_UsCo'] == $_SESSION['id']) { ?>
                    </br>
                    <div class="mensajes_usu">
                        <div class="div_logo perfil_pub">
                            <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $usec['Avatar']; ?>" alt="<?php echo $usec['Avatar']; ?>"><span style="white-space: pre"> </span>
                            <span class="a_nom_men" data-name="<?php echo $usec['Usuario']; ?>"><?php echo $usec['Nombre']; ?></span>
                            <div class="div_fecha"><span class="span_fec_men_usu"><?php echo $local_datetime->format('d-m-Y H:i'); ?></span></div></br>
                        </div>

                        <div class="div_mensaje">
                            <span class="span_mensaje_usu"><?php echo $textoChat; ?></span>
                        </div>
                    </div>

                <?php } else { ?>
                    </br>
                    <div class="mensajes">
                        <div class="div_logo perfil_pub">
                            <img class="avatar" data-src="./Frontend/static/media/styles/user/avatars/<?php echo $usec['Avatar']; ?>" alt="<?php echo $usec['Avatar']; ?>"><span style="white-space: pre"> </span>
                            <span class="a_nom_men" data-name="<?php echo $usec['Usuario']; ?>"><?php echo $usec['Nombre']; ?></span>
                            <div class="div_fecha"><span class="span_fec_men"><?php echo $local_datetime->format('d-m-Y H:i'); ?></span></div></br>
                        </div>

                        <div class="div_mensaje">
                            <span class="span_mensaje"><?php echo $textoChat; ?></span>
                        </div>
                    </div>

                <?php }
            }
            $res['res'] = ob_get_clean();
        }
    } */

    /*     public function getEmogis(&$res) //selecciona emotes del usuario
    {
        if ($_SESSION['TypeAcount'] != 'invitado') {
            ob_start();
            $connection = bdconnection();
            $veremo = mysqli_query($connection, "SELECT Nombre FROM purchases WHERE id_UsPu = " . $_SESSION['id'] . " AND Type = 'Emote'");
            $emotesPur = [];
            while ($resemo = mysqli_fetch_assoc($veremo)) {
                array_push($emotesPur, $resemo['Nombre']);
            }
            foreach ($emotesPur as $key => $value) {
                ?> <img id="img_emote_enviar-<?php echo $value ?>.webp" data-emote="<?php echo $value; ?>.webp" class="img_emote_enviar" src="./Frontend/static/media/emotes/<?php echo $value; ?>.webp" alt="<?php echo $value; ?>">
<?php }
            $res['emotesPur2'] = $emotesPur;
            $res['html'] = ob_get_clean();
        }
    } */

    /*     public function registerSurvey()
    {
        $connection = bdconnection();
        $pregunta = htmlspecialchars($_POST['Pregunta'], ENT_QUOTES, 'UTF-8');
        $opcion1 = htmlspecialchars($_POST['Opcion1'], ENT_QUOTES, 'UTF-8');
        $opcion2 = htmlspecialchars($_POST['Opcion2'], ENT_QUOTES, 'UTF-8');
        $opcion3 = $_POST['Opcion3'] != '' ? htmlspecialchars($_POST['Opcion3'], ENT_QUOTES, 'UTF-8') : '';
        $ver_len = true;

        if (strlen($pregunta) > 250 || strlen($opcion1) > 250 || strlen($opcion2) > 250 || (strlen($opcion3) > 250 && $opcion3 != null)) {
            $ver_len = false;
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">No se permite más de 250 caracteres en la Pregunta y Opciones:<br>
        Pregunta: ' . strlen($pregunta) . ' carcateres.<br>
        Opcion1: ' . strlen($opcion1) . ' carcateres.<br>
        Opcion2: ' . strlen($opcion2) . ' carcateres.<br>
        Opcion3: ' . strlen($opcion3) . ' carcateres.<br>
        </span>';
        }

        if ($ver_len) {
            $now = new \DateTime();
            $fechaExpFormatted = dateLocaltoUTC($_POST['fecExp'])->format('Y-m-d H:i');
            $minDate = clone $now; // Clonar $now para asegurar que no se modifique el objeto original
            $maxDate = clone $now;
            $minDateFormatted = $minDate->modify('+24 hours')->format('Y-m-d H:i'); // Formatear las fechas mínima y máxima en el formato deseado
            $maxDateFormatted = $maxDate->modify('+24 hours')->format('Y-m-d H:i');
            $fechaExpDB = dateLocaltoUTC($_POST['fecExp'])->format('Y-m-d H:i:s');
            if ($fechaExpFormatted >= $minDateFormatted && $fechaExpFormatted <= $maxDateFormatted) { // Verificar que la fecha de expiración esté dentro del rango permitido
                $sqlenc = mysqli_prepare($connection, "INSERT INTO surveys (id_UsEn, Fecha, Fecha_exp, Pregunta, Opcion1, Opcion2, Opcion3) VALUES (?, UTC_TIMESTAMP(), ?, ?, ?, ?, ?)");
                mysqli_stmt_bind_param($sqlenc, "isssss", $_SESSION['id'], $fechaExpDB, $pregunta, $opcion1, $opcion2, $opcion3);
                mysqli_stmt_execute($sqlenc);
                mysqli_stmt_close($sqlenc);

                if ($sqlenc) {
                    $res['success'] =  true;
                    $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Se publico Correctamente</span>';
                } else {
                    $res['res'] = '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error Interno</span>';
                }
            } else {
                $res['data'] = $fechaExpFormatted;
                $res['res'] = '<span class="cs-color-IntenseOrange text-center">Las fechas no son correctas, verifica que sea mayor a 24 horas y menor a 7 dias</span>';
            }
        }
    } */

    /*     public function registerPub()
    {
        $connection = bdconnection();
        $textarea = htmlspecialchars($_POST['publicacion'], ENT_QUOTES, 'UTF-8');
        $publicacion = nl2br($textarea);

        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = substr(str_shuffle($characters), 0, 12);
        $code = $randomString;
        $archivos = $_FILES['foto'] ?? null;
        $num_archivos = $archivos != null ? count($_FILES['foto']['name']) : 0;
        $archivos_nombres = array();

        $ver_extension = true;
        $ver_num_archivos = true;
        $ver_upload_file = true;
        $ver_len = true;

        if ($num_archivos > 5) { //Verificar cantidad máxima de archivos (5 archivos)
            $ver_num_archivos = false;
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">¡Limite de archivos superado: La cantidad máxima de archivos permitidos es 5, archivos importados: ' . $num_archivos . '!</span>';
        } else if (strlen($publicacion) > 1500) {
            $ver_len = false;
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">Publicacion muy larga, solo se permite hasta 1500 caracteres por Publicacion, caracteres insertados ' . strlen($publicacion) . '!</span>';
        } else if ($num_archivos > 0) {
            for ($i = 0; $i < $num_archivos; $i++) {
                $archivo = $archivos['name'][$i];
                $extension = strtolower(pathinfo($archivo, PATHINFO_EXTENSION));

                if ($extension != 'webp') { //Verificar extensión permitida (solo imágenes con formato webp)
                    $ver_extension = false;
                    $res['res'] = '<span class="cs-color-IntenseOrange text-center">¡Extensión no permitida para el archivo ' . $archivo . ', solo se permiten imágenes!</span>';
                } else {
                    $name = $code . "_" . $i . ".webp";

                    //Guardar el archivo en el servidor
                    if (!move_uploaded_file($archivos['tmp_name'][$i], "../Frontend/static/media/styles/user/publicaciones/imagenes/" . $name)) {
                        $ver_upload_file = false;
                        $res['res'] = '<span class="cs-color-IntenseOrange text-center">Error al subir los archivos</span>';
                    }
                    array_push($archivos_nombres, $name);
                }
            }

            $archivos_nombres_str = implode(",", $archivos_nombres);
        } else {
            $archivos_nombres_str = "";
        }

        if ($ver_extension && $ver_num_archivos && $ver_upload_file && $ver_len) {
            $subir = mysqli_prepare($connection, "INSERT INTO publications (id_UsPu, Contenido, Fecha, Archivo) VALUES (?, ?, UTC_TIMESTAMP(), ?)");
            mysqli_stmt_bind_param($subir, "iss", $_SESSION['id'], $publicacion, $archivos_nombres_str);
            mysqli_stmt_execute($subir);
            mysqli_stmt_close($subir);

            if ($subir) {
                $res['success'] = true;
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Se Publicó Correctamente</span>';
            } else {
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Ha ocurrido un error Interno</span>';
            }
        }
    } */

    /*     public function registerComent()
    {
        $connection = bdconnection();
        $comentario0 = htmlspecialchars($_POST['Comentario'], ENT_QUOTES, 'UTF-8');
        $comentario = nl2br($comentario0);
        $ver_len = true;

        if (strlen($comentario) > 1000) {
            $ver_len = false;
            $res['error'] = true;
            $res['res'] = '<script>cs-color-IntenseOrange text-center("Mensaje muy largo, solo se permite hasta 1000 caracteres por comentario, caracteres insertados: ' . strlen($comentario) . '")</script>';
        }

        if ($ver_len) {
            $insertCo = mysqli_prepare($connection, "INSERT INTO comments (id_UsCo,id_PuCo,Comentario,Fecha) VALUES (?,?,?,UTC_TIMESTAMP())");
            mysqli_stmt_bind_param($insertCo, "iis", $_SESSION['id'], $_POST['idPublicacionC'], $comentario);
            mysqli_stmt_execute($insertCo);
            mysqli_stmt_close($insertCo);

            $updateCo = mysqli_query($connection, "UPDATE publications SET Comentarios=Comentarios+1 WHERE id_Pub = '" . $_POST['idPublicacionC'] . "'");

            if ($insertCo && $updateCo) {
                $res['success'] =  true;
                $res['usuario'] =  $_SESSION['Usuario'];
                $res['nombre'] =  $_SESSION['Nombre'];
                $res['avatar'] =  $_SESSION['Avatar'];
                $datetime = toDateLocal(gmdate('Y-m-d H:i:s'));
                $res['datetime'] = $datetime->format('d-m-Y H:i');
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Se Mando el Comentario Correctamente</span>';
                //Notificacion('ins-not', $_POST['id_usu'], ['Title' => $_POST['nameusu'] . ' Comento tu publicación', 'Body' => 'Tienes un Nuevo Comentario de ' . $_POST['nameusu'] . ' en una de tus publicaciones', 'Icon' => './Frontend/static/media/styles/icons/info-circle.svg']);
            } else {
                $res['res'] = '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error Interno</span>';
            }
        }
    } */

    /*     public function insVot()
    {
        $connection = bdconnection();
        $idsug = isset($_POST['idSug']) ? $_POST['idSug'] : null;
        $id_Enc = isset($_POST['id_Enc']) ? $_POST['id_Enc'] : null;
        $opcion = isset($_POST['opcion']) ? $_POST['opcion'] : null;

        $sqlenc = mysqli_prepare($connection, "SELECT * FROM surveys WHERE id_Enc = ?");
        mysqli_stmt_bind_param($sqlenc, "i", $id_Enc);
        mysqli_stmt_execute($sqlenc);
        $resenc = mysqli_stmt_get_result($sqlenc);
        $resencarr = mysqli_fetch_assoc($resenc);

        // Comparar los timestamps
        if (strtotime(date('Y-m-d H:i:s')) >= strtotime($resencarr['Fecha_exp'])) { // Verifica si la fecha actual es mayor que la fecha de expiración
            $res['error'] = true;
            $res['res'] = 'La Encuesta ha terminado';
            return;
        }

        if ($idsug != null) {
            $sqlvotos = mysqli_prepare($connection, "SELECT * FROM sugerencias WHERE id_Sug = ? ");
            mysqli_stmt_bind_param($sqlvotos, "i", $idsug);
            mysqli_stmt_execute($sqlvotos);
            $rescomp = mysqli_stmt_get_result($sqlvotos);
            $votos = mysqli_fetch_assoc($rescomp);
            mysqli_stmt_close($sqlvotos);

            $comprobar4 = mysqli_prepare($connection, "SELECT * FROM votes WHERE id_UsVo = ? AND id_SuVo = ?");
            mysqli_stmt_bind_param($comprobar4, "ii", $_SESSION['id'], $idsug);
            mysqli_stmt_execute($comprobar4);
            $rescomp4 = mysqli_stmt_get_result($comprobar4);
            $count4 = mysqli_num_rows($rescomp4);
            $voto = mysqli_fetch_assoc($rescomp4);
            mysqli_stmt_close($comprobar4);

            if ($count4 == 0) {
                $insert = mysqli_prepare($connection, "INSERT INTO votes (id_UsVo,id_SuVo,Voto,Fecha) VALUES (?,?,?,UTC_TIMESTAMP()) ");
                mysqli_stmt_bind_param($insert, "iii", $_SESSION['id'], $idsug, $_1);
                mysqli_stmt_execute($insert);
                mysqli_stmt_close($insert);

                $subir = mysqli_prepare($connection, "UPDATE sugerencias SET Votos = Votos+1 WHERE id_Sug = ?");
                mysqli_stmt_bind_param($subir, "i", $idsug);
                mysqli_stmt_execute($subir);
                mysqli_stmt_close($subir);
            } else {
                if ($voto['Voto'] == 0) {
                    $insert2 = mysqli_prepare($connection, "UPDATE votes SET Voto = 1 WHERE id_UsVo = ? AND id_SuVo = ? and Voto = 0  ");
                    mysqli_stmt_bind_param($insert2, "ii", $_SESSION['id'], $idsug);
                    mysqli_stmt_execute($insert2);
                    mysqli_stmt_close($insert2);

                    $subir2 = mysqli_prepare($connection, "UPDATE sugerencias SET Votos = Votos+1 WHERE id_Sug = ?");
                    mysqli_stmt_bind_param($subir2, "i", $idsug);
                    mysqli_stmt_execute($subir2);
                    mysqli_stmt_close($subir2);
                } else {
                    $delete = mysqli_prepare($connection, "UPDATE votes SET Voto = 0 WHERE id_UsVo = ? AND id_SuVo = ? and Voto = ? ");
                    mysqli_stmt_bind_param($delete, "iii", $_SESSION['id'], $idsug, $_1);
                    mysqli_stmt_execute($delete);
                    mysqli_stmt_close($delete);

                    $up = mysqli_prepare($connection, "UPDATE sugerencias SET Votos = Votos-1 WHERE id_Sug = ?");
                    mysqli_stmt_bind_param($up, "i", $idsug);
                    mysqli_stmt_execute($up);
                    mysqli_stmt_close($up);
                }
            }
            $res['votos'] = $votos['Votos'];
            $res['count'] = $count4;
            $res['voto'] = $voto['Voto'];
        } elseif ($id_Enc != null) {
            $sqlenc = mysqli_prepare($connection, "SELECT * FROM surveys WHERE id_Enc = ? ");
            mysqli_stmt_bind_param($sqlenc, "i", $id_Enc);
            mysqli_stmt_execute($sqlenc);
            $resenc = mysqli_stmt_get_result($sqlenc);
            $votosenc = mysqli_fetch_assoc($resenc);
            mysqli_stmt_close($sqlenc);

            $comprobarenc = mysqli_prepare($connection, "SELECT * FROM votes WHERE id_UsVo = ? AND id_EnVo = ?");
            mysqli_stmt_bind_param($comprobarenc, "ii", $_SESSION['id'], $id_Enc);
            mysqli_stmt_execute($comprobarenc);
            $rescomenc = mysqli_stmt_get_result($comprobarenc);
            $countenc = mysqli_num_rows($rescomenc);
            $votoenc = mysqli_fetch_assoc($rescomenc);
            mysqli_stmt_close($comprobarenc);

            if ($countenc == 0) {
                $insert = mysqli_prepare($connection, "INSERT INTO votes (id_UsVo,id_EnVo,Voto,Opcion,Fecha) VALUES (?,?,?,?,UTC_TIMESTAMP()) ");
                mysqli_stmt_bind_param($insert, "iiii", $_SESSION['id'], $id_Enc, $_1, $opcion);
                mysqli_stmt_execute($insert);
                mysqli_stmt_close($insert);

                $subir = mysqli_prepare($connection, "UPDATE surveys SET Votos{$opcion} = Votos{$opcion}+1 WHERE id_Enc = ?");
                mysqli_stmt_bind_param($subir, "i", $id_Enc);
                mysqli_stmt_execute($subir);
                mysqli_stmt_close($subir);
                $res['rep'] = false;
                $res['new'] = true;
            } else {
                if ($votoenc['Voto'] == 0) {
                    $insert2 = mysqli_prepare($connection, "UPDATE votes SET Voto = 1, Opcion = ? WHERE id_UsVo = ? AND id_EnVo = ? and Voto = 0 ");
                    mysqli_stmt_bind_param($insert2, "iii", $opcion, $_SESSION['id'], $id_Enc);
                    mysqli_stmt_execute($insert2);
                    mysqli_stmt_close($insert2);

                    $subir2 = mysqli_prepare($connection, "UPDATE surveys SET Votos{$opcion} = Votos{$opcion}+1 WHERE id_Enc = ?");
                    mysqli_stmt_bind_param($subir2, "i", $id_Enc);
                    mysqli_stmt_execute($subir2);
                    mysqli_stmt_close($subir2);
                    $res['rep'] = false;
                    $res['new'] = true;
                } elseif ($votoenc['Voto'] == 1) {
                    if ($opcion == $votoenc['Opcion']) { //click a la misma opcion
                        $opcion_old = $votoenc['Opcion'];
                        $actenc = mysqli_prepare($connection, "UPDATE surveys SET Votos{$opcion_old} = Votos{$opcion_old}-1 WHERE id_Enc = ?");
                        mysqli_stmt_bind_param($actenc, "i", $id_Enc);
                        mysqli_stmt_execute($actenc);
                        mysqli_stmt_close($actenc);

                        $delete = mysqli_prepare($connection, "UPDATE votes SET Voto = 0, Opcion = 0 WHERE id_UsVo = ? AND id_EnVo = ? and Voto = 1");
                        mysqli_stmt_bind_param($delete, "ii", $_SESSION['id'], $id_Enc);
                        mysqli_stmt_execute($delete);
                        mysqli_stmt_close($delete);
                        $res['rep'] = true;
                        $res['new'] = false;
                    } else {
                        //emininar y actualizar voto
                        $opcion_old = $votoenc['Opcion'];
                        $actenc = mysqli_prepare($connection, "UPDATE surveys SET Votos{$opcion_old} = Votos{$opcion_old}-1 WHERE id_Enc = ?");
                        mysqli_stmt_bind_param($actenc, "i", $id_Enc);
                        mysqli_stmt_execute($actenc);
                        mysqli_stmt_close($actenc);

                        $insert2 = mysqli_prepare($connection, "UPDATE votes SET Opcion = ? WHERE id_UsVo = ? AND id_EnVo = ?");
                        mysqli_stmt_bind_param($insert2, "iii", $opcion, $_SESSION['id'], $id_Enc);
                        mysqli_stmt_execute($insert2);
                        mysqli_stmt_close($insert2);

                        $subir2 = mysqli_prepare($connection, "UPDATE surveys SET Votos{$opcion} = Votos{$opcion}+1 WHERE id_Enc = ?");
                        mysqli_stmt_bind_param($subir2, "i", $id_Enc);
                        mysqli_stmt_execute($subir2);
                        mysqli_stmt_close($subir2);
                        $res['rep'] = false;
                        $res['new'] = false;
                    }
                }
            }
            $sqlactenc = mysqli_prepare($connection, "SELECT * FROM surveys WHERE id_Enc = ? ");
            mysqli_stmt_bind_param($sqlactenc, "i", $id_Enc);
            mysqli_stmt_execute($sqlactenc);
            $resactenc = mysqli_stmt_get_result($sqlactenc);
            $votosactenc = mysqli_fetch_assoc($resactenc);
            mysqli_stmt_close($sqlactenc);

            $res['Votos1'] = $votosactenc['Votos1'];
            $res['Votos2'] = $votosactenc['Votos2'];
            $res['Votos3'] = $votosactenc['Votos3'];
        }
    } */

    /*     public function insReact()
    {
        $connection = bdconnection();
        $idUsuario = $_SESSION['id'];
        $idPublicacion = $_POST['id_pub'];
        $reac = $_POST['reac'];

        $sqlreac1 = mysqli_prepare($connection, "SELECT * FROM reactions WHERE id_PuRe = ? AND id_UsRe = ?");
        mysqli_stmt_bind_param($sqlreac1, "ii", $idPublicacion, $idUsuario);
        mysqli_stmt_execute($sqlreac1);
        $resmeg = mysqli_stmt_get_result($sqlreac1);
        $reacrow = mysqli_num_rows($resmeg);
        $reacarr = mysqli_fetch_assoc($resmeg);
        mysqli_stmt_close($sqlreac1);

        if ($reacrow == 0) {
            $insert = mysqli_prepare($connection, "INSERT INTO reactions (id_UsRe,id_PuRe,Reaccion,Fecha) VALUES (?,?,?,UTC_TIMESTAMP())");
            mysqli_stmt_bind_param($insert, "iii", $idUsuario, $idPublicacion, $reac);
            mysqli_stmt_execute($insert);
            mysqli_stmt_close($insert);

            $update = mysqli_prepare($connection, "UPDATE publications SET Reaccion$reac = Reaccion$reac+1 WHERE id_Pub = ?");
            mysqli_stmt_bind_param($update, "i", $idPublicacion);
            mysqli_stmt_execute($update);
            mysqli_stmt_close($update);
            $res['ins'] = true;
        } else if ($reacrow == 1) {
            if ($reacarr['Reaccion'] == 0) {
                $insert = mysqli_prepare($connection, "UPDATE reactions SET Reaccion = ? WHERE id_PuRe = ? AND id_UsRe = ?");
                mysqli_stmt_bind_param($insert, "iii", $reac, $idPublicacion, $idUsuario);
                mysqli_stmt_execute($insert);
                mysqli_stmt_close($insert);

                $update = mysqli_prepare($connection, "UPDATE publications SET Reaccion$reac = Reaccion$reac+1 WHERE id_Pub = ?");
                mysqli_stmt_bind_param($update, "i", $idPublicacion);
                mysqli_stmt_execute($update);
                mysqli_stmt_close($update);
                $res['ins'] = true;
            } else {
                $reac_old = $reacarr['Reaccion'];
                if ($reac == $reac_old) {
                    $delete = mysqli_prepare($connection, "UPDATE reactions SET Reaccion = 0 WHERE id_UsRe = ? AND id_PuRe = ?");
                    mysqli_stmt_bind_param($delete, "ii", $idUsuario, $idPublicacion);
                    mysqli_stmt_execute($delete);
                    mysqli_stmt_close($delete);

                    $update = mysqli_prepare($connection, "UPDATE publications SET Reaccion$reac_old = Reaccion$reac_old-1 WHERE id_Pub = ?");
                    mysqli_stmt_bind_param($update, "i", $idPublicacion);
                    mysqli_stmt_execute($update);
                    mysqli_stmt_close($update);
                    $res['rep'] = true;
                } else {
                    $reac_old = $reacarr['Reaccion'];
                    $delete = mysqli_prepare($connection, "UPDATE publications SET Reaccion$reac_old = Reaccion$reac_old-1 WHERE id_Pub = ?");
                    mysqli_stmt_bind_param($delete, "i", $idPublicacion);
                    mysqli_stmt_execute($delete);
                    mysqli_stmt_close($delete);

                    $insert = mysqli_prepare($connection, "UPDATE publications SET Reaccion$reac = Reaccion$reac+1 WHERE id_Pub = ?");
                    mysqli_stmt_bind_param($insert, "i", $idPublicacion);
                    mysqli_stmt_execute($insert);
                    mysqli_stmt_close($insert);

                    $update = mysqli_prepare($connection, "UPDATE reactions SET Reaccion = ? WHERE id_UsRe = ? AND id_PuRe = ?");
                    mysqli_stmt_bind_param($update, "iii", $reac, $idUsuario, $idPublicacion);
                    mysqli_stmt_execute($update);
                    mysqli_stmt_close($update);
                    $res['cam'] = true;
                }
            }
        }

        $sqlreac = mysqli_prepare($connection, "SELECT * FROM reactions WHERE id_PuRe=? AND id_UsRe = ?");
        mysqli_stmt_bind_param($sqlreac, "ii", $idPublicacion, $idUsuario);
        mysqli_stmt_execute($sqlreac);
        $resactmeg = mysqli_stmt_get_result($sqlreac);
        $reacactrow = mysqli_num_rows($resactmeg);
        $reacactarr = mysqli_fetch_assoc($resactmeg);
        mysqli_stmt_close($sqlreac);

        $sqlpubreac = mysqli_prepare($connection, "SELECT * FROM publications WHERE id_Pub = ?");
        mysqli_stmt_bind_param($sqlpubreac, "i", $idPublicacion);
        mysqli_stmt_execute($sqlpubreac);
        $respubreac = mysqli_stmt_get_result($sqlpubreac);
        $pubreacrow = mysqli_num_rows($respubreac);
        $pubreacarr = mysqli_fetch_assoc($respubreac);
        mysqli_stmt_close($sqlpubreac);

        $res['Reaccion1'] = $pubreacarr['Reaccion1'];
        $res['Reaccion2'] = $pubreacarr['Reaccion2'];
        $res['Reaccion3'] = $pubreacarr['Reaccion3'];
        $res['Reaccion4'] = $pubreacarr['Reaccion4'];
    } */
}
