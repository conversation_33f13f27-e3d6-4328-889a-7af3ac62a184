CREATE TABLE
    products (
        product_id VARCHAR(255) PRIMARY KEY,
        name <PERSON><PERSON>M ("gems", "coins", "deckanalyzer", "deckbuilder") NOT NULL,
        category ENUM ("CURRENCY", "TOOL") NOT NULL,
        description TEXT,
        amount INT NOT NULL,
        currency_price ENUM ("USD", "GEM", "COIN", "FREE") NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        state ENUM ("ACTIVE", "INACTIVE") DEFAULT "ACTIVE",
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;