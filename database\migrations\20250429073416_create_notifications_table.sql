CREATE TABLE
  `notifications` (
    `id_Not` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsNo` INT NOT NULL,
    `Title` VARCHAR(50) NOT NULL,
    `Body` VARCHAR(500) NOT NULL,
    `Icon` VARCHAR(50) NOT NULL,
    `Visto` TINYINT (1) default 0,
    `Fecha` DATETIME NOT NULL,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Not_Usu` FOREIGN KEY (`id_UsNo`) REFERENCES `users` (`id_Usu`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;