<?php

namespace App\Views;

class View
{
    public $view;
    public $data;

    public function __construct($view, $data = [])
    {
        if (!file_exists(PATH_ROOT . 'App/Views/' . $view . '.php')) {
            throw new \Exception("View file not found: " . $view);
        }

        $this->view = $view;
        $this->data = $data;
    }

    public function render()
    {
        $data = $this->data;

        ob_start();
        if ($this->view == 'ShowCards') {
            require_once PATH_ROOT . '/App/Views/' . $this->view . '.php';
            return $res;
        } else {
            require_once PATH_ROOT . '/App/Views/' . $this->view . '.php';
            return ob_get_clean();
        }
    }
}
