<?php

namespace App\Controllers;

use App\Config\Config;
use App\Views\View;

class AboutUsController
{

    public static function getSettings()
    {
        $View = new View('SettingsView');
        return $View->render();
    }

    public static function CSVersion()
    {
        $dateCS = Config::getDateCS();
        $resgetInfCS['title'] = 'Version';
        $resgetInfCS['content'] = $dateCS['content'];
        $resgetInfCS['date'] = $dateCS['date'];
        return $resgetInfCS;
    }

    public static function showAbout()
    {
        $csData = json_decode(file_get_contents(PATH_ROOT . 'App/Data/ClashStrategic.json'), true);
        $View = new View('AboutView', array_merge($csData, ['CSDate' => static::CSVersion()['content'], 'date' => static::CSVersion()['date'], 'version' => Config::getDateCS()['version']]));
        $resgetInfCS['title'] = 'Acerca De';
        $resgetInfCS['content'] = $View->render();

        return $resgetInfCS;
    }

    public static function showPresentationCs()
    {
        $csData = json_decode(file_get_contents(PATH_ROOT . 'App/Data/ClashStrategic.json'), true);
        $View = new View('PresentationCsView', $csData);
        $resgetInfCS['inf']['title'] = '¡Bienvenido!';
        $resgetInfCS['inf']['content'] = $View->render();
        return $resgetInfCS;
    }

    public static function showAboutUs()
    {
        $csData = json_decode(file_get_contents(PATH_ROOT . 'App/Data/ClashStrategic.json'), true);
        $View = new View('AboutUsView', $csData);
        $resgetInfCS['title'] = 'SobreNosotros';
        $resgetInfCS['content'] = $View->render();
        return $resgetInfCS;
    }

    public static function RegLog($type) //obtener informacion sobre CS
    {
        $viewSL = match ($type) {
            'SignIn' => 'SignInView',
            'SignUp' => 'SignUpView',
            default => throw new \InvalidArgumentException("Invalid type: $type"),
        };

        $View = new View($viewSL);
        $resgetInfCS['inf']['title'] = $type == 'SignIn' ? 'Iniciar Sesión' : 'Registrarse';
        $resgetInfCS['inf']['content'] = $View->render();
        return $resgetInfCS;
    }

    public static function verCSVersion(&$res) //verifica que version esta corriendo en el cliente
    {
        $v = static::CSVersion()['date'];
        $versionActual = strtotime($v);
        $versionCookie = strtotime($_COOKIE['CSDate'] ?? false);

        if ($versionActual == false)
            throw new \Exception('Error al obtener la versionActual de CS');

        if ($versionCookie == false) {
            setcookie('CSDate', $v, 0, '/'); //establece la cookie en la ruta raíz si no existe
            setcookie('CSVersion', $v, 0, '/'); //establece la cookie en la ruta raíz si no existe
            $res['newVersion'] = false;
        } elseif ($versionCookie == $versionActual) { //la version del cliente es la misma que la original
            $res['newVersion'] = false;
        } elseif ($versionCookie < $versionActual) { //la version es antigua a la version original
            setcookie('CSDate', $v, 0, '/');
            setcookie('CSVersion', $v, 0, '/');
            $res['newVersion'] = true;
        } else {
            $res['newVersion'] = false;
        }

        $res['date'] = $v;
        $res['version'] = Config::getDateCS()['version'];
        return $res;
    }

    public static function termcond()
    {
    }
}
