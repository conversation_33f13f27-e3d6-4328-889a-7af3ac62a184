CREATE TABLE
  `strategies` (
    `id_Str` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsSt` INT NOT NULL,
    `Estado` VARCHAR(50) DEFAULT 'Activo' NOT NULL,
    `Visibilidad` VARCHAR(50) DEFAULT 'Publico' NOT NULL,
    `Nombre` VARCHAR(100) NOT NULL,
    `Des<PERSON>ripcion` TEXT NOT NULL,
    `PrecioGema` INT DEFAULT 0 NOT NULL,
    `Condiciones` JSON DEFAULT NULL,
    `<PERSON><PERSON>o` DATETIME NOT NULL,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Str_Usu` FOREIGN KEY (`id_UsSt`) REFERENCES `users` (`id_Usu`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;