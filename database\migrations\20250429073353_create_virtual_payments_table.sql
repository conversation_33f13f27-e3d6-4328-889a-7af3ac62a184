CREATE TABLE
  `virtual_payments` (
    `id_Pur` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsPu` INT NOT NULL,
    `purchase` VARCHAR(15) NOT NULL,
    `price` INT NOT NULL,
    `currency` ENUM ('free', 'coin', 'gem') NOT NULL,
    `quantity` INT NOT NULL,
    `status` ENUM ('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed' NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Pur_Usu` FOREIGN KEY (`id_UsPu`) REFERENCES `users` (`id_Usu`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;