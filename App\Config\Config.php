<?php

namespace App\Config;

class Config
{

    public static $CS_API_VERSION = "0.7.0";
    public static $CS_API_RELEASE_DATETIME = "2024-08-01T00:00:00Z";

    public static function getCrApiKey($type): string
    {
        $keys = [
            "local" => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiIsImtpZCI6IjI4YTMxOGY3LTAwMDAtYTFlYi03ZmExLTJjNzQzM2M2Y2NhNSJ9.eyJpc3MiOiJzdXBlcmNlbGwiLCJhdWQiOiJzdXBlcmNlbGw6Z2FtZWFwaSIsImp0aSI6IjVlYzgzM2FlLWMxZjktNDFhZS1hZWZiLTkwOWYyZmRmMTVkMyIsImlhdCI6MTY5NTM4ODAxMSwic3ViIjoiZGV2ZWxvcGVyL2IxYmEyZDFlLWNkMWUtODc5YS1kYTllLTUyZDcyY2Y2MTZhMCIsInNjb3BlcyI6WyJyb3lhbGUiXSwibGltaXRzIjpbeyJ0aWVyIjoiZGV2ZWxvcGVyL3NpbHZlciIsInR5cGUiOiJ0aHJvdHRsaW5nIn0seyJjaWRycyI6WyIxNjkuMjU0LjE5Mi4xNTgiXSwidHlwZSI6ImNsaWVudCJ9XX0.aXqF9ehQCdBiaUpPVeZUNOAF83Kfp8FPvcQ7kl16RPN7WpH-Z2wmYOA3gz-fh-hhgyuhE4XpmFfrIzzSqh_19A',
            "epizy" => "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiIsImtpZCI6IjI4YTMxOGY3LTAwMDAtYTFlYi03ZmExLTJjNzQzM2M2Y2NhNSJ9.eyJpc3MiOiJzdXBlcmNlbGwiLCJhdWQiOiJzdXBlcmNlbGw6Z2FtZWFwaSIsImp0aSI6ImI1YTI3ZWI4LWQwNjQtNDQ0Yi1hYzE1LTZkMzMyODBhNjI2MCIsImlhdCI6MTY4MjUxNDQ5Mywic3ViIjoiZGV2ZWxvcGVyL2IxYmEyZDFlLWNkMWUtODc5YS1kYTllLTUyZDcyY2Y2MTZhMCIsInNjb3BlcyI6WyJyb3lhbGUiXSwibGltaXRzIjpbeyJ0aWVyIjoiZGV2ZWxvcGVyL3NpbHZlciIsInR5cGUiOiJ0aHJvdHRsaW5nIn0seyJjaWRycyI6WyIxODUuMjcuMTM0LjE5NyJdLCJ0eXBlIjoiY2xpZW50In1dfQ.99zEUDomhoIhgNeWOkE78yKskw6zXoIMJWFjcly7idooyBQGmFKTGtajrZYc3EYw5vKH--qAzQwlPazJJ3RRng",
            "great-site" => "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiIsImtpZCI6IjI4YTMxOGY3LTAwMDAtYTFlYi03ZmExLTJjNzQzM2M2Y2NhNSJ9.eyJpc3MiOiJzdXBlcmNlbGwiLCJhdWQiOiJzdXBlcmNlbGw6Z2FtZWFwaSIsImp0aSI6IjY5MTM4NzQ0LTFlYzgtNDY3Yi04MDMyLTI3N2ZiYTZhZmIzNyIsImlhdCI6MTczODc1OTUxNiwic3ViIjoiZGV2ZWxvcGVyL2IxYmEyZDFlLWNkMWUtODc5YS1kYTllLTUyZDcyY2Y2MTZhMCIsInNjb3BlcyI6WyJyb3lhbGUiXSwibGltaXRzIjpbeyJ0aWVyIjoiZGV2ZWxvcGVyL3NpbHZlciIsInR5cGUiOiJ0aHJvdHRsaW5nIn0seyJjaWRycyI6WyIxODUuMjcuMTM0LjIxOSJdLCJ0eXBlIjoiY2xpZW50In1dfQ.uklxjE39xUH7S8DeqaYbP4kV5d2SBqA-aidWnSfBbq2QbWFC905bsGMXih9iG0eDQtKJOXf_mXsbxAxgiMm24w",
            //mas keys aqui
        ];
        if (!array_key_exists($type, $keys)) {
            throw new \Exception('Key "' . $type . '" not found');
        }
        $key = $keys[$type];
        return $key;
    }

    public static function getDateCS(): array
    {
        $version = self::$CS_API_VERSION;
        $formatDate = function ($dateString) {
            $date = new \DateTime($dateString, new \DateTimeZone('UTC'));
            return $date->format('Y-m-d');
        };
        $date = $formatDate(self::$CS_API_RELEASE_DATETIME);
        $contentDate = date('d F Y', strtotime($date));

        return [
            'content' => $contentDate,
            'date' => $date,
            'version' => $version
        ];
    }

    public static function msgDBNotEnabled()
    {
        $arryMsg = ["state" => 'dbNotEnabled'];
        return $arryMsg;
    }

    public static function getParamDB($serverName): array
    {
        if ($serverName === 'localhost' || $serverName === '127.0.0.1') {
            $host = '127.0.0.1';
            $dbname = 'clash_strategic';
            $usuario = 'root';
            $contrasena = '61379963111Gg@';
        } elseif ($serverName === 'clashstrategic.com' || $serverName === 'clashstrategic.great-site.net') {
            $host = 'sql104.epizy.com';
            $dbname = 'epiz_33242539_clash_strategic';
            $usuario = 'epiz_33242539';
            $contrasena = '9u3rcUX9of';
        } elseif ($serverName === 'clashstrategic.x10.mx') {
            $host = '';
            $dbname = '';
            $usuario = '';
            $contrasena = '';
        } else {
            throw new \Exception("Database connection error: Invalid server name provided.");
        }

        return ['host' => $host, 'dbname' => $dbname, 'usuario' => $usuario, 'contrasena' => $contrasena];
    }

    public static function datetimeNowUTC() //datetime UTC
    {
        return gmdate('Y-m-d H:i:s');
    }

    public static function toDateLocal($datetime) //convertir de utc a local
    {
        $user_timezone = new \DateTimeZone($_SESSION['Timezone']);
        $datetime_utc = new \DateTime($datetime, new \DateTimeZone('UTC'));
        $local_datetime = $datetime_utc->setTimezone($user_timezone);
        return $local_datetime;
    }

    public static function dateLocaltoUTC($datetimeLocal)
    {
        $user_timezone = new \DateTimeZone($_SESSION['Timezone']);
        $dateTime = new \DateTime($datetimeLocal, $user_timezone); // Crea el objeto DateTime con la zona horaria local
        $dateTime->setTimezone(new \DateTimeZone('UTC')); // Cambia la zona horaria a UTC
        return $dateTime; // Retorna el objeto DateTime
    }

    public static function generarToken() //token de 32 caracteres, bin2hex() es de 32 bits
    {
        return bin2hex(random_bytes(16));
    }

    public static function record($action)
    {
        file_put_contents('../Logs/userActivity.txt', sprintf("[%s] IP: %s - Usuario: %s - TypeAcount: %s  - Acción: %s\n", date("Y-m-d H:i:s"), $_SERVER['REMOTE_ADDR'], ($_SESSION['TypeAcount'] != 'null' ? $_SESSION['Usuario'] : 'invitado'), $_SESSION['TypeAcount'], $action), FILE_APPEND);
    }

    public static function getjson($type) //obtener un json
    {
        return json_decode(file_get_contents(PATH_ROOT . '/App/Data/' . $type));
    }

    public static function getDeviceType()
    {
        $userAgent = $_SERVER['HTTP_USER_AGENT'];
        // Lista de patrones para detectar diferentes tipos de dispositivos
        $mobilePatterns = '/(android|iphone|ipad|ipod|blackberry|bb|playbook|windows phone|webos|palm|symbian|meego|bada|tizen|maemo|fennec|mobile|tablet|opera mini|opera mobi|kindle|silk|midp|avantgo|plucker|xiino|puffin|mmp|pocket|mobileexplorer|motorola|nokia|panasonic|webtv|bolt|boost|cricket|docomo|fone|hiptop|htc|lg|samsung|sonyericsson|telit|up.browser|up.link|vodafone|wap|sagem|smartphone|miui|mibrowser|tablet)/i';
        $tabletPatterns = '/(ipad|android(?!.*mobile)|tablet|playbook|silk|kindle|nexus\s*[0-9]+.*|sm-t|tablet|gt-p|sgp|xoom|sch-i800|a100|a101|nook|transformer|tf101)/i';

        if (preg_match($mobilePatterns, $userAgent)) {
            return 'mobile';
        } elseif (preg_match($tabletPatterns, $userAgent)) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }

    public static function initialSetup() //configuraciones iniciales
    {
        date_default_timezone_set('UTC'); //todos los date que se invoquen en el php seran en utc
        $_SERVER['SERVER_NAME'] === 'localhost' && error_reporting(E_ALL); //activa muestra de advertencias, errores etc solo para locahost
        if (session_status() == PHP_SESSION_NONE) { //mediadas para las cookies
            ini_set('session.cookie_secure', '1'); // Solo envía la cookie sobre HTTPS
            ini_set('session.cookie_httponly', '1'); // No permite acceso a la cookie desde JavaScript
            ini_set('session.cookie_samesite', 'Strict'); // Esta opción ayuda a mitigar ciertos tipos de ataques de CSRF al restringir cómo se envían las cookies con solicitudes de origen cruzado.
        }

        //para manejar las excepciones no capturadas.
        set_exception_handler(function ($exception) {
            $mensaje = gmdate('Y-m-d H:i:s') . " >> " . $exception->getMessage() . " en " . $exception->getFile() . " en la línea " . $exception->getLine() . PHP_EOL;
            $_SERVER['SERVER_NAME'] == 'localhost' ? file_put_contents('./LogsDev/ServerError.log', $mensaje, FILE_APPEND) :
                file_put_contents('./App/Data/Logs/ServerError.log', $mensaje, FILE_APPEND);
            echo "<pre><strong>Error:</strong> {$mensaje}</pre>";
        });
    }

    public static function validateTimezone($timezone)
    {
        return in_array($timezone, timezone_identifiers_list());
    }

    public static function es(&$res)
    {
        $res['data'] = static::getjson('languages/es.json');
    }

    public static function checkSeguriry(&$res)
    {
        /*         $TypeAcount = TypeAcount();
        if ($TypeAcount != 'invitado' && $TypeAcount != 'invitado' && $_SESSION['UserAgent'] !== $_SERVER['HTTP_USER_AGENT']) {
            $res['state'] = 'error';
            array_push($res['alerts'], 'ERROR');
        } */
    }

    /**
     * Valida y decodifica un archivo JSON.
     *
     * Verifica la existencia, legibilidad y validez del contenido JSON de un archivo.
     * Lanza excepciones si alguna verificación falla.
     *
     * @param string $filePath La ruta completa al archivo JSON.
     * @param bool $associative Si es true, devuelve un array asociativo; si es false (predeterminado), devuelve un objeto.
     * @return mixed El contenido decodificado del JSON (objeto o array asociativo).
     * @throws \Exception Si el archivo no existe, no se puede leer o el JSON no es válido.
     */
    public static function validateAndDecodeJsonFile(string $filePath, bool $associative = false)
    {
        if (!file_exists($filePath)) {
            throw new \Exception("El archivo JSON no existe: {$filePath}");
        }

        $jsonData = file_get_contents($filePath);
        if ($jsonData === false) {
            throw new \Exception("No se pudo leer el archivo JSON: {$filePath}");
        }

        $decodedData = json_decode($jsonData, $associative);
        if ($decodedData === null && json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Error al decodificar el JSON del archivo {$filePath}: " . json_last_error_msg());
        }

        return $decodedData;
    }

    /*     public function updates(&$res)
    {
        $TypeAcount = TypeAcount();

        // Verificar chat
        $chat = mysqli_query($connection, "SELECT COUNT(*) AS num_rows_cha FROM chat");
        $row_cha = mysqli_fetch_assoc($chat);
        $num_rows_cha = $row_cha['num_rows_cha'];

        $_POST['pre_num_row_cha'] == 0 ? $pre_num_row_cha = $num_rows_cha : $pre_num_row_cha = $_POST['pre_num_row_cha'];
        if ($num_rows_cha > $pre_num_row_cha) {
            $res['new_cha'] = true;
            $res['pre_num_row_cha'] = $num_rows_cha;
        } else {
            $res['new_cha'] = false;
            $res['pre_num_row_cha'] = $pre_num_row_cha;
        }

        // Verificar publicaciones y encuestas
        $pub = mysqli_query($connection, "SELECT COUNT(*) AS num_rows_pub FROM publications");
        $row_pub = mysqli_fetch_assoc($pub);
        $num_rows_pub = $row_pub['num_rows_pub'];

        $enc = mysqli_query($connection, "SELECT COUNT(*) AS num_rows_enc FROM surveys");
        $row_enc = mysqli_fetch_assoc($enc);
        $num_rows_enc = $row_enc['num_rows_enc'];

        $_POST['pre_num_row_pub'] == 0 ? $pre_num_row_pub = $num_rows_pub : $pre_num_row_pub = $_POST['pre_num_row_pub'];
        if ($num_rows_pub > $pre_num_row_pub) {
            $res['new_pub'] = true;
            $res['pre_num_row_pub'] = $num_rows_pub;
        } else {
            $res['new_pub'] = false;
            $res['pre_num_row_pub'] = $pre_num_row_pub;
        }

        $_POST['pre_num_row_enc'] == 0 ? $pre_num_row_enc = $num_rows_enc : $pre_num_row_enc = $_POST['pre_num_row_enc'];
        if ($num_rows_enc > $pre_num_row_enc) {
            $res['new_enc'] = true;
            $res['pre_num_row_enc'] = $num_rows_enc;
        } else {
            $res['new_enc'] = false;
            $res['pre_num_row_enc'] = $pre_num_row_enc;
        }

        if ($TypeAcount == 'sinSesion') { //ver si la session sigue Activa para enviar la respuesta en mensaje
            $session = $res['session'] = false;
        } else {
            $session = $res['session'] = true;
        }
        $chat && $pub && $enc && $session ? $res['log'] = 'success' : $res['log'] = 'error en unas consultas'; //finalizacion
    } */
}
