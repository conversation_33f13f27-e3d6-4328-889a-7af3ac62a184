<?php

namespace App\Services;

class Paypal
{
    public static function getCredendialsPaypal() //obtener las credenciales
    {
        if ($_SERVER['SERVER_NAME'] === 'localhost') { //credenciales SandBox
            $clientID = 'AREgyPLe2tZEk0paIMQhP3VXkz1pUqJh6iN70G5D0I5iFYFlUSfoB5XUAsUxQPBCWY569bD6mRRx1-k8';
            $clientSecret = 'EPxEo5eiFTDHsFE8pIjrrHhQSAjx822efttxM7g-GhOzbBmhjEHnx2qkL-M_OuyiAc9yMzH2ZqxxlYj-';
        } else { //credenciales Live
            $clientID = 'AR48KcKlfPTf1nMcrrbNXsCt5Vg_KhziEh7NSSPPSeouPmS2udZKsgO46vNeodsmgNwUEQDfD8JadNfn';
            $clientSecret = 'EEE8M9LFu6UnevnQmrkJbQqmYnhT36zEm3R4e6jcmabg7ggRS8v1K3S46vLj93aWXa4PSTYL3k4DawD7';
        }
        return ['clientID' => $clientID, 'clientSecret' => $clientSecret];
    }

    public static function paypalAPI($type, $data = null)
    {
        switch ($type) {
            case 'cre-tok': //crear access token
                $typeCurl = 1;
                $customRequest = "POST";
                $postfields = 'grant_type=client_credentials';
                $credentials = static::getCredendialsPaypal();
                $authorization = 'basic ' . base64_encode($credentials['clientID'] . ':' . $credentials['clientSecret']);
                $contentType = 'application/x-www-form-urlencoded';
                $_SERVER['SERVER_NAME'] === 'localhost' ? ($paypalUrl = 'https://api.sandbox.paypal.com/v1/oauth2/token') : ($paypalUrl = 'https://api.paypal.com/v1/oauth2/token');
                break;
            case 'ord-det': //detalles de una orden
                $typeCurl = 2;
                $customRequest = "GET";
                $postfields = 'grant_type=client_credentials';
                $authorization = 'Bearer ' .  $data['accessToken'];
                $contentType = 'application/Utils/json';
                $_SERVER['SERVER_NAME'] === 'localhost' ? ($paypalUrl = 'https://api.sandbox.paypal.com/v2/checkout/orders/' . $data['orderID']) : ($paypalUrl = 'https://api.paypal.com/v2/checkout/orders/' . $data['orderID']);
                break;
            case 'cre-ord': //crear una orden
                $typeCurl = 2;
                $customRequest = "POST";
                $authorization = 'basic ' .  'EPxEo5eiFTDHsFE8pIjrrHhQSAjx822efttxM7g-GhOzbBmhjEHnx2qkL-M_OuyiAc9yMzH2ZqxxlYj-';
                $contentType = 'application/x-www-form-urlencoded';
                $_SERVER['SERVER_NAME'] === 'localhost' ? ($paypalUrl = 'https://api.sandbox.paypal.com/v2/checkout/orders') : ($paypalUrl = 'https://api.paypal.com/v2/checkout/orders');
                $postfields = '{"intent": "CAPTURE","purchase_units": [{"reference_id": "PUHF","amount": {"currency_code": "USD","value": ' . $data['cost'] . '}}],"application_context": {"return_url": "","cancel_url": ""}}';
                break;
            case 'cap-pay': //captturar la orden y hacer el pago
                $typeCurl = 2;
                $customRequest = "POST";
                $authorization = 'Bearer ' .  $data['accessToken'];
                $contentType = 'application/Utils/json';
                $_SERVER['SERVER_NAME'] === 'localhost' ? ($paypalUrl = "https://api.sandbox.paypal.com/v2/checkout/orders/" . $data['orderID'] . "/capture") : ($paypalUrl = "https://api.paypal.com/v2/checkout/orders/" . $data['orderID'] . "/capture");
                break;
        }
        if ($typeCurl == 1) {
            $curl = curl_init();
            if ($_SERVER['SERVER_NAME'] === 'localhost') {
                curl_setopt($curl, CURLOPT_PROXY, '**********');
                curl_setopt($curl, CURLOPT_PROXYPORT, 7071);
            }
            curl_setopt_array($curl, array(
                CURLOPT_URL => $paypalUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $postfields,
                CURLOPT_HTTPHEADER => array(
                    'accept: application/Utils/json',
                    'accept-language: en_US',
                    'authorization: ' . $authorization,
                    'content-type: ' . $contentType
                ),
            ));
        } else {
            $curl = curl_init();
            if ($_SERVER['SERVER_NAME'] === 'localhost') {
                curl_setopt($curl, CURLOPT_PROXY, '**********');
                curl_setopt($curl, CURLOPT_PROXYPORT, 7071);
            }
            curl_setopt_array($curl, array(
                CURLOPT_URL => $paypalUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => $customRequest,
                CURLOPT_HTTPHEADER => array(
                    'authorization: ' . $authorization,
                    'content-type: ' . $contentType
                ),
            ));
        }

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($response) { //success
            return ['data' => json_decode($response), 'status' => 'success'];
        } else { //error
            return ['data' => "cURL Error #:" . $err . ' response: ' . $response, 'status' => 'error'];
        }
    }
}
