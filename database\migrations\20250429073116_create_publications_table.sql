CREATE TABLE
  `publications` (
    `id_Pub` INT PRIMARY KEY AUTO_INCREMENT NOT NULL,
    `id_UsPu` INT NOT NULL,
    `<PERSON>tenido` VARCHAR(2000) NULL,
    `Fecha` DATETIME NOT NULL,
    `Archivo` VARCHAR(1500) NULL,
    `Comentarios` INT DEFAULT '0',
    `Reaccion1` INT DEFAULT '0',
    `Reaccion2` INT DEFAULT '0',
    `<PERSON><PERSON>cion3` INT DEFAULT '0',
    `Reaccion4` INT DEFAULT '0',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT `fk_Pub_Usu` FOREIGN KEY (`id_UsPu`) REFERENCES `users` (`id_Usu`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;